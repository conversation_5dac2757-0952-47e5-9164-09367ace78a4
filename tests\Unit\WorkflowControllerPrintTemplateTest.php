<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Controllers\Workflow\WorkflowController;
use App\Services\FileUploadService;
use App\Models\PrintTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Mockery;

class WorkflowControllerPrintTemplateTest extends TestCase
{
    use RefreshDatabase;

    protected $workflowController;
    protected $fileUploadService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock FileUploadService
        $this->fileUploadService = Mockery::mock(FileUploadService::class);
        
        // Create controller instance with mocked service
        $this->workflowController = new WorkflowController(
            app('App\Repositories\Process\ProcessRepositoryInterface'),
            app('App\Repositories\Form\FormRepositoryInterface'),
            app('App\Repositories\Field\FieldRepositoryInterface'),
            app('App\Repositories\ProcessVersion\ProcessVersionRepositoryInterface'),
            app('App\Repositories\Stage\StageRepositoryInterface'),
            app('App\Services\JobPermissionService'),
            $this->fileUploadService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_successfully_saves_print_templates()
    {
        // Arrange
        $processVersionId = 'test-process-version-id';
        $files = [
            UploadedFile::fake()->create('template1.docx', 100),
            UploadedFile::fake()->create('template2.pdf', 200)
        ];

        $uploadedFilesData = [
            'file_names' => ['template1-12.00.00-01.01.2024.docx', 'template2-12.00.01-01.01.2024.pdf'],
            'file_path' => ['file_templates/template1-12.00.00-01.01.2024.docx', 'file_templates/template2-12.00.01-01.01.2024.pdf'],
            'original_filename' => ['template1.docx', 'template2.pdf'],
            'type' => ['docx', 'pdf']
        ];

        $this->fileUploadService
            ->shouldReceive('uploadTemplateFiles')
            ->once()
            ->with($files, 'file_templates')
            ->andReturn($uploadedFilesData);

        // Act
        $result = $this->workflowController->savePrintTemplate($files, $processVersionId);

        // Assert
        $this->assertTrue($result);
        
        // Verify database records
        $this->assertDatabaseCount('print_templates', 2);
        
        $this->assertDatabaseHas('print_templates', [
            'file_name' => 'template1-12.00.00-01.01.2024.docx',
            'original_filename' => 'template1.docx',
            'type' => 'docx',
            'process_version_id' => $processVersionId
        ]);
        
        $this->assertDatabaseHas('print_templates', [
            'file_name' => 'template2-12.00.01-01.01.2024.pdf',
            'original_filename' => 'template2.pdf',
            'type' => 'pdf',
            'process_version_id' => $processVersionId
        ]);
    }

    /** @test */
    public function it_returns_true_when_no_files_provided()
    {
        // Arrange
        $processVersionId = 'test-process-version-id';
        $files = [];

        $this->fileUploadService
            ->shouldReceive('uploadTemplateFiles')
            ->once()
            ->with($files, 'file_templates')
            ->andReturn([]);

        // Act
        $result = $this->workflowController->savePrintTemplate($files, $processVersionId);

        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseCount('print_templates', 0);
    }

    /** @test */
    public function it_rolls_back_and_cleans_up_files_when_database_creation_fails()
    {
        // Arrange
        $processVersionId = 'test-process-version-id';
        $files = [UploadedFile::fake()->create('template1.docx', 100)];

        $uploadedFilesData = [
            'file_names' => ['template1-12.00.00-01.01.2024.docx'],
            'file_path' => ['file_templates/template1-12.00.00-01.01.2024.docx'],
            'original_filename' => ['template1.docx'],
            'type' => ['docx']
        ];

        $this->fileUploadService
            ->shouldReceive('uploadTemplateFiles')
            ->once()
            ->with($files, 'file_templates')
            ->andReturn($uploadedFilesData);

        // Create a fake file to simulate uploaded file
        Storage::fake('local');
        Storage::put('file_templates/template1-12.00.00-01.01.2024.docx', 'fake content');

        // Mock PrintTemplate::create to fail
        $originalCreate = PrintTemplate::class . '::create';
        
        // Force database error by using invalid process_version_id
        $invalidProcessVersionId = null;

        // Act & Assert
        $this->expectException(\Exception::class);

        try {
            $this->workflowController->savePrintTemplate($files, $invalidProcessVersionId);
        } catch (\Exception $e) {
            // Verify the exception message contains our expected text
            $this->assertStringContainsString('Failed to create print template record', $e->getMessage());
            throw $e; // Re-throw for expectException to catch
        }

        // Verify no database records were created
        $this->assertDatabaseCount('print_templates', 0);
    }

    /** @test */
    public function it_handles_file_upload_service_exception()
    {
        // Arrange
        $processVersionId = 'test-process-version-id';
        $files = [UploadedFile::fake()->create('template1.docx', 100)];

        $this->fileUploadService
            ->shouldReceive('uploadTemplateFiles')
            ->once()
            ->with($files, 'file_templates')
            ->andThrow(new \Exception('File upload failed'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('File upload failed');

        $this->workflowController->savePrintTemplate($files, $processVersionId);

        // Verify no database records were created
        $this->assertDatabaseCount('print_templates', 0);
    }
}
