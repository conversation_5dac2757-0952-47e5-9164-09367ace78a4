<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;

class FileUploadService
{
    /**
     * Tải lên các file và trả về danh sách tên file mới
     */
    public function uploadFiles($files, $storage_path, $sleep_time = 1)
    {
        if (empty($files)) {
            return [];
        }
        
        $file_names = [];
        
        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $file_name = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();
                
                // Tạo độ trễ giữa mỗi lần upload để tránh trùng tên
                sleep($sleep_time);
                
                $current_time = now()->format('H.i.s');
                $current_date = now()->format('d.m.Y');
                $file_name_new = $file_name . '-' . $current_time . '-' . $current_date . '.' . $extension;
                $file_names[] = $file_name_new;
                
                // Lưu file vào thư mục storage
                $file->storeAs($storage_path, $file_name_new);
            }
        }
        
        return $file_names;
    }

    public function uploadTemplateFiles($files, $storage_path, $sleep_time = 1)
    {
        if (empty($files)) {
            return [];
        }
        
        $file_names = [];
        $original_filename = [];
        $type = [];
        $file_path = [];
        
        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $file_name = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();
                $type[] = $extension;
                
                // Tạo độ trễ giữa mỗi lần upload để tránh trùng tên
                sleep($sleep_time);
                
                $current_time = now()->format('H.i.s');
                $current_date = now()->format('d.m.Y');
                $file_name_new = $file_name . '-' . $current_time . '-' . $current_date . '.' . $extension;
                $file_names[] = $file_name_new;
                $original_filename[] = $file_name;
                
                // Lưu file vào thư mục storage
                $real_file_path = $file->storeAs($storage_path, $file_name_new);
                $file_path[] = $real_file_path;
            }
        }
        
        return [
            'file_names' => $file_names,
            'original_filename' => $original_filename,
            'type' => $type,
            'file_path' => $file_path
        ]; 
    }
    
    /**
     * Tải lên một file duy nhất và trả về tên file mới
     */
    public function uploadSingleFile($file, $storage_path, $sleep_time = 1)
    {
        if (!$file instanceof UploadedFile) {
            return null;
        }
        
        $file_name = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        
        // Tạo độ trễ
        sleep($sleep_time);
        
        $current_time = now()->format('H.i.s');
        $current_date = now()->format('d.m.Y');
        $file_name_new = $file_name . '-' . $current_time . '-' . $current_date . '.' . $extension;
        
        // Lưu file vào thư mục storage
        $file->storeAs($storage_path, $file_name_new);
        
        return $file_name_new;
    }
    
    /**
     * Xử lý tải lên file từ form
     */
    public function processFormFileUploads($file_uploads, $form_key)
    {
        if (empty($file_uploads) || empty($form_key)) {
            return [];
        }
        
        $result = [];
        
        foreach ($file_uploads as $key => $files) {
            if ($key === $form_key) {
                $result = $this->uploadFiles($files, 'file_uploads');
            }
        }
        
        return $result;
    }
    
    /**
     * Xử lý tải lên file từ bảng
     */
    public function processTableFileUploads($key_file_upload_childrens)
    {
        if (empty($key_file_upload_childrens)) {
            return [];
        }
        
        $result = [];
        
        foreach ($key_file_upload_childrens as $child_key => $child_values) {
            $result[$child_key] = [];
            
            foreach ($child_values as $key => $files) {
                $result[$child_key][$key] = $this->uploadFiles($files, 'file_uploads');
            }
        }
        
        return $result;
    }
} 