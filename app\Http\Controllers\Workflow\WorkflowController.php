<?php

namespace App\Http\Controllers\Workflow;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Process\ProcessRepositoryInterface;
use App\Repositories\Form\FormRepositoryInterface;
use App\Repositories\Field\FieldRepositoryInterface;
use App\Repositories\Stage\StageRepositoryInterface;
use App\Repositories\ProcessVersion\ProcessVersionRepositoryInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Field;
use App\Models\Stage;
use App\Models\Condition;
use App\Models\Action;
use App\Models\EmailTemplate;
use App\Models\SyncGroup;
use App\Models\StageEmailConfig;
use App\Models\EmailCondition;
use App\Models\StageTransition;
use App\Models\StageTransitionCondition;
use App\Models\StageSyncGroup;
use App\Models\ProcessTransition;
use App\Models\ProcessTransitionsCondition;
use App\Models\PrintTemplate;
use App\Enums\FieldStatus;
use App\Services\JobPermissionService;
use App\Enums\ProcessVersionStatus;
use App\Services\FileUploadService;
use Illuminate\Support\Facades\Log;

class WorkflowController extends Controller
{
    private $processRepository;
    private $formRepository;
    private $fieldRepository;
    private $processVersionRepository;
    private $stageRepository;
    private $jobPermissionService;
    protected $fileUploadService;

    public function __construct(
        ProcessRepositoryInterface $processRepository,
        FormRepositoryInterface $formRepository,
        FieldRepositoryInterface $fieldRepository,
        ProcessVersionRepositoryInterface $processVersionRepository,
        StageRepositoryInterface $stageRepository,  
        JobPermissionService $jobPermissionService,
        FileUploadService $fileUploadService
    ) 
    {
        $this->processRepository = $processRepository;
        $this->formRepository  = $formRepository;
        $this->fieldRepository = $fieldRepository;
        $this->processVersionRepository = $processVersionRepository;
        $this->stageRepository = $stageRepository;
        $this->jobPermissionService = $jobPermissionService;
        $this->fileUploadService = $fileUploadService;
    }

    public function getFlowTransitions($workflowID)
    {
        try {
            $user = Auth::user();
            $process = $this->processRepository->find($workflowID);
            $process_version = $process->processVersionActive;
            $process_version_id = $process_version->id;
            $data_flow_transitions = $this->jobPermissionService->getFlowTransitionsForJob($job = null, $user, $process_version_id);
            
            return response()->json([
                'status' => 'success',
                'data_flow_transitions' => $data_flow_transitions,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'error_code' => 'SERVER_ERROR',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getAllWorkflows(Request $request)
    {
        $dataSearch = $request->all();
        $result = $this->processRepository->getAllWorkflowRes($dataSearch);
        
        if (isset($result)) {
            
            return response()->json([
                'status' => 'success',
                'counts' => $result['counts'],
                'workflows' => $result['workflows'],
            ], 200);
        } else {

            return response()->json(['status' => 'not_data'], 200);
        }
    }

    public function checkDuplicateKeywords($fields)
    {
        $collection = collect($fields);

        // Lấy tất cả các keyword từ mảng
        $keywords = $collection->pluck('keyword');

        // Lấy tất cả các value trong childrens (nếu có)
        // $childrens_values = $collection->flatMap(function($item) {
        //     return isset($item['childrens']) ? collect($item['childrens'])->pluck('value') : [];
        // });

        // Kết hợp tất cả các keyword và value
        // $all_keywords = $keywords->merge($childrens_values);

        // Đếm số lần xuất hiện của mỗi keyword và value
        $keyword_counts = $keywords->countBy();

        // Lọc ra các giá trị trùng lặp (count > 1)
        $duplicates = $keyword_counts->filter(function($count) {
            return $count > 1; // Chỉ lấy các giá trị xuất hiện nhiều lần
        });
        if ($duplicates->isNotEmpty()) {
            return true;
        }

        return false;
    }

    public function nullIfEmpty($value) {
        $value = trim($value);

        return ($value === "") ? null : $value;
    }

    public function saveForm($form)
    {
        // Kiểm tra nếu dữ liệu form không tồn tại
        if (!$form) {
            return response()->json([
                'status' => 'error',
                'error_code' => 'INVALID_FORM_DATA',
            ], 400);
        } 

        $data_form = [
            'name'        => $form['name'],
            'description' => $form['description'] ?? null,
        ];

        // Lưu thông tin form
        $form_created = $this->formRepository->create($data_form);

        return $form_created->id;
    }

    public function saveProcess($workflow, $status)
    {
        // Kiểm tra nếu dữ liệu workflow không tồn tại
        if (!$workflow) {
            return response()->json([
                'status' => 'error',
                'error_code' => 'INVALID_WORKFLOW_DATA',
            ], 400);
        }
        
        // Tiếp tục xử lý lưu process với các giá trị đã lấy
        $data_process = [
            'name'             => $workflow['name'],
            'description'      => $workflow['description'] ?? null,
            'process_group_id' => $workflow['process_group']['value'] ?? null,
            'status'           => $status,
            'create_by'        => Auth::id(),
        ];
        // Lưu thông tin process
        $process_created = $this->processRepository->create($data_process);
        
        return $process_created->id;
    }

    public function saveProcessVersion($process_id, $form_id, $workflow_version)
    {
        // Kiểm tra nếu dữ liệu workflow_version không tồn tại
        if (!$workflow_version) {
            return response()->json([
                'status' => 'error',
                'error_code' => 'INVALID_WORKFLOW_DATA',
            ], 400);
        }

        // Hàm chung để lấy giá trị từ các trường
        $extract_values = function ($field) use ($workflow_version) {
            if (isset($workflow_version[$field]) && is_array($workflow_version[$field]) && count($workflow_version[$field]) > 0) {
                return array_column($workflow_version[$field], 'value');
            }
            return null;
        };
        // Lấy giá trị cho các trường
        $scope_use = $extract_values('scope_use');
        $followers = $extract_values('followers');
        $job_manager = $extract_values('job_manager');
        $process_manager = $extract_values('process_manager');

        $this->processRepository->find($process_id)->processVersions()->update(['is_active' => ProcessVersionStatus::FALSE->value]);
        $count_process_version = $this->processRepository->find($process_id)->processVersionsWithoutActiveScope()->count();
        
        if ($count_process_version > 0) {
            $version_number = $count_process_version + 1;
            $old_version = $version_number - 1;
            $change_log = [
                'action' => 'đã tạo mới',
                'old_version' => $old_version,
                'new_version' => $version_number,
            ];
        } else {
            $version_number = 1;
            $change_log = [
                'action' => 'đã tạo mới',
            ];
        }
        
        $data_process_version = [
            'process_id'      => $process_id,
            'form_id'         => $form_id,
            'scope_use'       => $scope_use,
            'followers'       => $followers,
            'process_manager' => $process_manager,
            'job_manager'     => $job_manager,
            'version_number'  => $version_number,
            'change_log'      => $change_log,
            'is_active'       => ProcessVersionStatus::TRUE->value,
            'create_by'       => Auth::id(),
        ];

        // Lưu thông tin process_version
        $process_version_created = $this->processVersionRepository->create($data_process_version);

        return $process_version_created->id;
    }

    public function savePrintTemplate($print_template_files, $process_version_id)
    {
        try {
            // --- Xử lý Print Template ---
            $files = $this->fileUploadService->uploadTemplateFiles($print_template_files, 'file_templates');

            if (empty($files)) {
                return true; // Không có file để xử lý
            }

            $uploaded_file_paths = [];

            // Lưu danh sách file paths để có thể xóa nếu cần rollback
            foreach ($files['file_path'] as $file_path) {
                $uploaded_file_paths[] = storage_path("app/{$file_path}");
            }

            DB::beginTransaction();

            foreach ($files['file_names'] as $key => $file_name) {
                try {
                    $print_template_created = PrintTemplate::create([
                        'filed_name'          => $file_name,
                        'file_path'          => $files['file_path'][$key],
                        'original_filename'  => $files['original_filename'][$key],
                        'type'               => $files['type'][$key],
                        'process_version_id' => $process_version_id,
                    ]);

                    if (!$print_template_created) {
                        throw new \Exception('Failed to create print template record');
                    }
                } catch (\Exception $e) {
                    // Nếu tạo record thất bại, rollback và xóa files
                    DB::rollBack();
                    $this->cleanupUploadedFiles($uploaded_file_paths);

                    // Throw với message rõ ràng hơn
                    throw new \Exception('Failed to create print template record: ' . $e->getMessage());
                }
            }

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();

            // Xóa các file đã upload nếu có lỗi
            if (isset($uploaded_file_paths)) {
                $this->cleanupUploadedFiles($uploaded_file_paths);
            }

            // Log lỗi để debug
            Log::error('Error in savePrintTemplate: ' . $e->getMessage(), [
                'process_version_id' => $process_version_id,
                'files_count' => isset($files) ? count($files['file_names'] ?? []) : 0
            ]);

            throw $e; // Re-throw để caller có thể xử lý
        }
    }

    /**
     * Xóa các file đã upload khi có lỗi
     */
    private function cleanupUploadedFiles($file_paths)
    {
        foreach ($file_paths as $file_path) {
            if (file_exists($file_path)) {
                try {
                    unlink($file_path);
                } catch (\Exception $e) {
                    Log::warning('Failed to delete uploaded file: ' . $file_path . ' - ' . $e->getMessage());
                }
            }
        }
    }

    public function saveField($fields, $processedStageIdMap, $process_version_id, $form_id)
    {
        // --- Xử lý Field ---
        if (empty($fields)) {
            return response()->json([
                'status' => 'error',
                'error_code' => 'INVALID_FIELD_DATA',
            ], 400);
        }

        $result = $this->checkDuplicateKeywords($fields);

        if ($result) {
            return response()->json([
                'status' => 'error',
                'error_code' => 'KEY_DUPLICATE',
            ], status: 400);
        }
        $field_created = collect($fields)->map(function ($field) use ($processedStageIdMap, $process_version_id, $form_id){
            if (isset($field['min_equal']) && $field['min_equal'] === "") {
                $field['min_equal'] = null;
            }
            // --- Xử lý Field Stage ---
            $fieldStageId = null;
            if (isset($field['stages']['slug'])) {
                $fieldStageSlug = trim($field['stages']['slug']);
                // Ưu tiên kiểm tra map
                if (isset($processedStageIdMap[$fieldStageSlug])) {
                    $fieldStageId = $processedStageIdMap[$fieldStageSlug];
                } else {
                    // 7. Nếu không có trong map, tìm trong CSDL bằng Model GiaiDoan
                    $backStageUuid = Stage::where('process_version_id', $process_version_id)
                        ->where('slug', $fieldStageSlug)
                        ->value('id'); // Chỉ lấy giá trị cột 'id' (UUID)

                    // 8. Gán ID nếu tìm thấy
                    if ($backStageUuid) {
                        $fieldStageId = $backStageUuid;
                    }
                }
            } else if (is_array($field['stages']) && count($field['stages']) === 1) {
                $fieldStageId = current($field['stages']); // Lấy định danh đặc biệt (vd: 'start')
            }
            
            $data_field = [
                'keyword'         => $field['keyword'],
                'display_name'    => $field['display_name'],
                'display_name_en' => $this->nullIfEmpty($field['display_name_en'] ?? null),
                'type'            => $field['type'],
                'default_value'   => isset($field['default_value']) ? (is_array($field['default_value']) ? (empty($field['default_value']) ? null : $field['default_value']) : $this->nullIfEmpty($field['default_value'])) : null,
                'required'        => is_numeric($field['required'] ?? false) ? $field['required'] : (($field['required'] ?? false) === true ? FieldStatus::TRUE->value : FieldStatus::FALSE->value),
                'order'           => $this->nullIfEmpty($field['order'] ?? null),
                'min_equal'       => $this->nullIfEmpty($field['min_equal'] ?? null),
                'max_equal'       => $this->nullIfEmpty($field['max_equal'] ?? null),
                'placeholder'     => $this->nullIfEmpty($field['placeholder'] ?? null),
                'placeholder_en'  => $this->nullIfEmpty($field['placeholder_en'] ?? null),
                'column_width'    => $this->nullIfEmpty($field['column_width'] ?? null),
                'not_edit'        => is_numeric($field['not_edit'] ?? false) ? $field['not_edit'] : (($field['not_edit'] ?? false) === true ? FieldStatus::TRUE->value : FieldStatus::FALSE->value),
                'options'         => isset($field['options']) ? $field['options'] : null,
                'multiple'        => is_numeric($field['multiple'] ?? false) ? $field['multiple'] : (($field['multiple'] ?? false) === true ? FieldStatus::TRUE->value : FieldStatus::FALSE->value),
                'object_table'    => $this->nullIfEmpty($field['object_table'] ?? null),
                'column_table'    => $this->nullIfEmpty($field['column_table'] ?? null),
                'sub_column_table' => !empty($field['column_table_sub']) ? $field['column_table_sub'] : null,
                'stage_id'        => $fieldStageId,
                'form_id'         => $form_id,
                'is_active'       => FieldStatus::TRUE->value,
                'create_by'       => Auth::id(),
            ];
            // dd($data_field);
            return $this->fieldRepository->create($data_field);
        });
        
        $field_created->each(function ($field, $index) use ($fields, $form_id) {
            // Truy xuất lại phần tử từ mảng $data tại vị trí $index
            $item = $fields[$index]; 
            
            // Kiểm tra nếu phần tử có trường 'childrens'
            if (isset($item['childrens']) && $item['type'] == 'TABLE') {
                // Lấy ID của bản ghi chứa childrens (ID của bản ghi này là ID của field)
                $parent_id = $field->id;
                // Duyệt qua tất cả các childrens
                collect($item['childrens'])->each(function ($child) use ($parent_id, $form_id) {
                    // Tìm tất cả các bản ghi có keyword trùng với value của childrens và form_id
                    $records_to_update = Field::where('keyword', $child['value'])
                        ->where('form_id', $form_id)
                        ->whereNull('parent_id')
                        ->get();

                    // Cập nhật parent_id cho các bản ghi này
                    $records_to_update->each(function ($record) use ($parent_id) {
                        $record->update(['parent_id' => $parent_id]);  // Cập nhật parent_id với ID của bản ghi chứa childrens
                    });
                });
            }
            
            // Kiểm tra nếu phần tử có trường 'children'
            if (isset($item['children']) && $item['type'] == 'TABLE') {
                // Lấy ID của bản ghi chứa childrens (ID của bản ghi này là ID của field)
                $parent_id = $field->id;
                // Duyệt qua tất cả các childrens
                collect($item['children'])->each(function ($child) use ($parent_id, $form_id) {
                    // Tìm tất cả các bản ghi có keyword trùng với value của childrens và form_id
                    $records_to_update = Field::where('keyword', $child['keyword'])
                        ->where('form_id', $form_id)
                        ->whereNull('parent_id')
                        ->get();

                    // Cập nhật parent_id cho các bản ghi này
                    $records_to_update->each(function ($record) use ($parent_id) {
                        $record->update(['parent_id' => $parent_id]);  // Cập nhật parent_id với ID của bản ghi chứa childrens
                    });
                });
            }
        });
    }

    public function showWorkflow($workflowId)
    {
        try {
            $workflow_active = $this->processRepository->showWorkflowDetailActive($workflowId);

            if (!$workflow_active) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Workflow not found'
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'data_workflow' => $workflow_active
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get workflow details: ' . $e->getMessage()
            ], 500);
        }
    }

    public function updateInfoWorkflow(Request $request, $workflowId)
    {
        try {
            $workflow = $this->processRepository->find($workflowId);
            
            if (!$workflow) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Workflow not found'
                ], 404);
            }
            
            $workflow->update([
                'name' => $request->name,
                'description' => $request->description,
                'process_group_id' => $request->process_group_id,
            ]);
            
            return response()->json([
                'status' => 'success',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update workflow: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Helper function to extract values from approver/followers array
     * Kiểm tra nếu không có 'value' property thì sẽ lấy toàn bộ item
     */
    private function extractValues($dataItem)
    {
        if (is_string($dataItem)) {
            return $dataItem;
        }

        if (empty($dataItem) || !is_array($dataItem)) {
            return null;
        }

        $result = [];
        foreach ($dataItem as $item) {
            if (is_array($item) && isset($item['value'])) {
                // Nếu có 'value' property thì lấy value
                $result[] = $item['value'];
            } else {
                // Nếu không có 'value' property thì lấy toàn bộ item
                $result = $dataItem;
            }
        }
        
        return $result;
    }

    private function extractNotKeyValue($dataItem)
    {
        if (is_string($dataItem)) {
            return $dataItem;
        }

        if (empty($dataItem) || !is_array($dataItem)) {
            return null;
        }

        $result = [];
        foreach ($dataItem as $item) {
            $result = $dataItem['value'];
        }
        
        return $result;
    }

    public function storeWorkflow(Request $request)
    {
        try {
            $workflow_id = $request->workflowId;
            $status = $request->statusSubmit;
            $form   = json_decode($request->dataFormWorkflow, true);
            $workflow = json_decode($request->dataWorkflow, true);
            $workflow_version = json_decode($request->dataWorkflowVersion, associative: true);
            $setup_process = json_decode($request->dataSetupProcess, true);
            // dd($setup_process);
            $fields = json_decode($request->fieldSetups, true);
            $email_template_files = $request->emailTemplateFiles ?? null;
            $email_template_back_files = $request->emailTemplateBackFiles ?? null;
            $print_template_files = $request->printTemplateFiles ?? null;
            $sleep_second = 1; // Thời gian sleep
            // dd($request->all());
            // dd($fields);
            // Bắt đầu giao dịch
            
            DB::beginTransaction();

            // Lưu thông tin form
            $form_id = $this->saveForm($form);

            if (isset($workflow_id)) {
                $workflow = $this->processRepository->find($workflow_id);
                $workflow->status = $status;
                $workflow->save();
                $process_id = $workflow->id;
            } else {
                // Lưu thông tin workflow
                $process_id = $this->saveProcess($workflow, $status);
            }
            
            // Lưu thông tin processVersion
            $process_version_id = $this->saveProcessVersion($process_id, $form_id, $workflow_version);
            // Lưu thông tin biểu mẫu
            $this->savePrintTemplate($print_template_files, $process_version_id);
            // Lưu thông tin luồng phê duyệt
            if (empty($setup_process)) {
                return response()->json([
                    'status' => 'error',
                    'error_code' => 'INVALID_SETUP_PROCESS_DATA',
                ], 400);
            } else {
                $processedStageIdMap         = []; // Map: stageName         => stageId (Tối ưu trong 1 lần chạy)
                $processedActionIdMap        = []; // Map: actionName        => actionId
                $processedConditionIdMap     = []; // Map: conditionName     => conditionId
                $processedEmailTemplateIdMap = []; // Map: emailTemplatename => emailTemplateId
                $processedSyncGroupIdMap     = []; // Map: emailTemplatename => syncGroupId
                
                foreach ($setup_process as $index => $process) {
                    $fromStageId         = null;
                    $toStageId           = null;
                    $actionId            = null;
                    $conditionId         = null;
                    $emailTemplateId     = null;
                    $syncGroupId         = null;
                    $stageEmailConfigId  = null;
                    $backToStageId       = null;
                    $stageTransitionId   = null;
                    $actionBackToId      = null;
                    $emailTemplateBackId = null;

                    // --- Xử lý From Stage (Dùng firstOrCreate nếu có slug) ---
                    if (isset($process['from_stage']) && isset($process['from_stage']['slug'])) {
                        $stageSlug = trim($process['from_stage']['slug']);
                        // Ưu tiên kiểm tra map
                        if (isset($processedStageIdMap[$stageSlug])) {
                            $fromStageId = $processedStageIdMap[$stageSlug];
                        } else {
                            // Dùng firstOrCreate để tìm hoặc tạo mới
                            $stage = Stage::firstOrCreate(
                                [
                                    'process_version_id' => $process_version_id, 
                                    'slug' => $stageSlug
                                ],
                                [ /* Chỉ điền các trường này nếu tạo mới */
                                    'name' => $this->nullIfEmpty($process['from_stage']['name'] ?? null),
                                    'description' => $this->nullIfEmpty($process['from_stage']['description'] ?? null),
                                    'approver' => $this->extractValues($process['from_stage']['approver'] ?? null),
                                    'followers' => $this->extractValues($process['from_stage']['followers'] ?? null),
                                    'comment' => !empty($process['from_stage']['comment']) ? FieldStatus::TRUE->value : FieldStatus::FALSE->value,
                                ]
                            );
                            
                            $fromStageId = $stage->id;
                            $processedStageIdMap[$stageSlug] = $stage->id; // Cập nhật map
                        }
                    } else if (is_array($process['from_stage']) && count($process['from_stage']) === 1) {
                        $fromStageId = current($process['from_stage']); // Lấy định danh đặc biệt (vd: 'start')
                    } else { 
                        throw new \Exception("Transition #{$index}: Cấu trúc from_stage không hợp lệ."); 
                    }

                    // --- Xử lý To Stage (Dùng firstOrCreate nếu có slug) ---
                    if (isset($process['to_stage']) && isset($process['to_stage']['slug'])) {
                        $stageSlug = trim($process['to_stage']['slug']);
                        // Ưu tiên kiểm tra map
                        if (isset($processedStageIdMap[$stageSlug])) {
                            $toStageId = $processedStageIdMap[$stageSlug];
                        } else {
                            // Dùng firstOrCreate để tìm hoặc tạo mới
                            $stage = Stage::firstOrCreate(
                                [
                                    'process_version_id' => $process_version_id, 
                                    'slug' => $stageSlug
                                ],
                                [
                                    /* Chỉ điền các trường này nếu tạo mới */
                                    // Quan trọng: Lấy thông tin từ to_stage nếu có để tạo
                                    'name' => $this->nullIfEmpty($process['to_stage']['name'] ?? null),
                                    'description' => $this->nullIfEmpty($process['to_stage']['description'] ?? null),
                                    'approver' => $this->extractValues($process['to_stage']['approver'] ?? null),
                                    'followers' => $this->extractValues($process['to_stage']['followers'] ?? null),
                                    'comment' => !empty($process['to_stage']['comment']) ? FieldStatus::TRUE->value : FieldStatus::FALSE->value,
                                ]
                            );
                            
                            $toStageId = $stage->id;
                            $processedStageIdMap[$stageSlug] = $stage->id; // Cập nhật map
                        }
                    } else if (is_array($process['to_stage']) && count($process['to_stage']) === 1) {
                        $toStageId = current($process['to_stage']); // Lấy định danh đặc biệt (vd: 'done')
                    }

                    // --- Xử lý Action Next (Giữ nguyên logic trước) ---
                    if (isset($process['action_next']) && isset($process['action_next']['slug'])) {
                        $actionSlug = trim($process['action_next']['slug']);
                        // Ưu tiên kiểm tra map
                        if (isset($processedActionIdMap[$actionSlug])) { 
                            $actionId = $processedActionIdMap[$actionSlug]; 
                        } else { 
                            /* ... Action::firstOrCreate ... */
                            $action = Action::firstOrCreate(
                                [
                                    'process_version_id' => $process_version_id, 
                                    'slug' => $actionSlug
                                ],
                                [
                                    'name' => $this->nullIfEmpty($process['action_next']['name'] ?? null),
                                    'description' => $this->nullIfEmpty($process['action_next']['description'] ?? null),
                                ]
                            );
                            $actionId = $action->id;
                            $processedActionIdMap[$actionSlug] = $action->id;
                        }
                    } else if (is_array($process['action_next']) && count($process['action_next']) === 1) {
                        $actionId = current($process['action_next']);
                    } else { 
                        throw new \Exception("Transition #{$index}: Cấu trúc action_next không hợp lệ.");
                    }

                    // --- Xử lý Condition ---
                    if (isset($process['condition']) && isset($process['condition']['slug'])) {
                        $conditionSlug = trim($process['condition']['slug']);
                        $orConditionsData = $process['condition']['or_conditions'];
                        if (is_array($orConditionsData)) {
                            $this->transformOrConditionValues($orConditionsData); // Sửa đổi $orConditionsData trực tiếp
                        }
                        // Kiểm tra map trước để tối ưu
                        if (isset($processedConditionIdMap[$conditionSlug])) {
                            $conditionId = $processedConditionIdMap[$conditionSlug];
                        } else {
                            // Lưu condition vào bảng conditions
                            $condition = Condition::firstOrCreate(
                                [
                                    'process_version_id' => $process_version_id,
                                    'slug' => $conditionSlug,
                                ],
                                [
                                    // *** Chỉ lưu JSON của orConditions vào cột 'or_conditions' khi tạo mới ***
                                    'name' => $this->nullIfEmpty($process['condition']['name'] ?? null),
                                    'or_conditions' => $orConditionsData
                                ]
                            );
                            // dump($condition);
                            $conditionId = $condition->id;
                            $processedConditionIdMap[$conditionSlug] = $conditionId;
                        }
                    }

                    // --- Xử lý Email Template ---
                    // Kiểm tra xem email_template có tồn tại, là mảng và có 'slug' không
                    if (isset($process['email_template']) && isset($process['email_template']['slug'])) {
                        $emailSlug = trim($process['email_template']['slug']);
                        $nestedConditionId = null; // ID cho condition *bên trong* email template
                        // 1. Xử lý condition lồng bên trong email_template trước
                        // Kiểm tra cấu trúc ['condition']['value']
                        if (isset($process['email_template']['condition']['value']) && isset($process['email_template']['condition']['value']['slug'])) {
                            $nestedConditionSlug = trim($process['email_template']['condition']['value']['slug']);
                            $nestedOrConditions = $process['email_template']['condition']['value']['or_conditions'] ?? null;

                            // Biến đổi 'p' trong orConditions lồng nhau
                            if (is_array($nestedOrConditions)) {
                                $this->transformOrConditionValues($nestedOrConditions);
                            }

                            // Tìm hoặc tạo Condition lồng nhau này
                            if (isset($processedConditionIdMap[$nestedConditionSlug])) {
                                $nestedConditionId = $processedConditionIdMap[$nestedConditionSlug];
                            } else {
                                $nestedCond = Condition::firstOrCreate(
                                    [
                                        'process_version_id' => $process_version_id,
                                        'slug' => $nestedConditionSlug
                                    ],
                                    [
                                        'name' => $process['email_template']['condition']['value']['name'],
                                        'or_conditions' => $nestedOrConditions !== null ? $nestedOrConditions : null
                                    ]
                                );
                                $nestedConditionId = $nestedCond->id;
                                $processedConditionIdMap[$nestedConditionSlug] = $nestedConditionId; // Cập nhật map chung
                            }
                        } // Kết thúc xử lý condition lồng nhau

                        // 2. Tìm hoặc tạo EmailTemplate, liên kết với condition_id (nếu có)
                        // Kiểm tra map email trước
                        if (isset($processedEmailTemplateIdMap[$emailSlug])) {
                            $emailTemplateId = $processedEmailTemplateIdMap[$emailSlug];
                        } else {
                            // Kiểm tra và lưu file tương ứng
                            $file_name_news = [];
                            if (isset($email_template_files[$index])) {
                                foreach ($email_template_files[$index] as $file) {
                                    if (is_string($file)) {
                                        $file_name_news[] = $file;
                                    } else {
                                        $file_name = $file->getClientOriginalName();
                                        $extension = $file->getClientOriginalExtension();
                                        // Tạo độ trễ 1 giây giữa mỗi lần upload
                                        sleep($sleep_second); 
                                        $current_time = now()->format('H.i.s');
                                        $current_date = now()->format('d.m.Y');
                                        $file_name_new = $file_name . '-' . $current_time . '-' . $current_date . '.' . $extension;
                                        $file_name_news[] = $file_name_new;
                                        //Lưu file chứng từ vào thư mục file_upload_emails
                                        $file->storeAs('file_upload_emails', $file_name_new);
                                    }
                                }
                            }
                            $emailTemplate = EmailTemplate::firstOrCreate(
                                [
                                    'process_version_id' => $process_version_id,
                                    'slug' => $emailSlug
                                ],
                                [
                                    'name' => $this->nullIfEmpty($process['email_template']['name'] ?? null),
                                    'name_title' => $this->nullIfEmpty($process['email_template']['name_title'] ?? null),
                                    // 'condition_id' => $nestedConditionId, // << Liên kết tới condition đã xử lý
                                    'from_email' => $this->extractNotKeyValue($process['email_template']['from_email']) ?? null,
                                    'to_emails' => $this->extractValues($process['email_template']['to_emails']) ?? null,
                                    'cc_emails' => $this->extractValues($process['email_template']['cc_emails']) ?? null,
                                    'bcc_emails' => $this->extractValues($process['email_template']['bcc_emails']) ?? null,
                                    'content' => $this->nullIfEmpty($process['email_template']['content'] ?? null),
                                    'files' => !empty($file_name_news) ? $file_name_news : null,
                                ]
                            );
                            $emailTemplateId = $emailTemplate->id;
                            $processedEmailTemplateIdMap[$emailSlug] = $emailTemplateId; // Cập nhật map email
                        }
                    } // Kết thúc xử lý email_template

                    // --- Xử lý Sync Group ---
                    // Kiểm tra xem sync_group có tồn tại, là mảng và có 'name' hợp lệ không
                    if (isset($process['sync_group']) && isset($process['sync_group']['slug'])) {
                        $syncGroupSlug = trim($process['sync_group']['slug']);
                        // Kiểm tra map trước để tối ưu
                        if (isset($processedSyncGroupIdMap[$syncGroupSlug])) {
                            $syncGroupId = $processedSyncGroupIdMap[$syncGroupSlug];
                        } else {
                            // Dùng firstOrCreate để tìm hoặc tạo mới SyncGroup dựa trên workflow_id và name
                            $syncGroup = SyncGroup::firstOrCreate(
                                [
                                    'process_version_id' => $process_version_id,
                                    'slug' => $syncGroupSlug,
                                ],
                                [
                                    'name' => $this->nullIfEmpty($process['sync_group']['name'] ?? null),
                                ]
                            );

                            // Lấy ID của SyncGroup (dù là tìm thấy hay mới tạo)
                            $syncGroupId = $syncGroup->id;
                            // Cập nhật map
                            $processedSyncGroupIdMap[$syncGroupSlug] = $syncGroupId;
                        }
                    }

                    // --- Xử lý Stage Sync Group ---
                    // Chỉ lưu nếu có ID (UUID) hợp lệ cho cả ba thành phần
                    // Sử dụng isset() để kiểm tra biến đã được gán giá trị khác null chưa
                    if (isset($fromStageId) && isset($syncGroupId) && isset($actionId)) {
                        // Sử dụng firstOrCreate trên Model StageSyncGroup để đảm bảo không trùng lặp
                        // Dựa trên sự kết hợp của cả 3 UUID.
                        // Sử dụng firstOrCreate và lấy về đối tượng để có thể lấy ID
                        StageSyncGroup::firstOrCreate(
                            [
                                // Điều kiện tìm kiếm liên kết đã tồn tại
                                'stage_id' => $fromStageId,
                                'sync_group_id' => $syncGroupId,
                                'action_id' => $actionId,
                            ]
                            // Không cần mảng thứ hai vì không có cột nào khác cần set giá trị khi tạo
                        );
                    } // Kết thúc xử lý lưu vào stage_sync_groups

                    // --- Xử lý Stage Email Config ---
                    // Chỉ lưu nếu có ID (UUID) hợp lệ cho cả ba thành phần
                    // Sử dụng isset() để kiểm tra biến đã được gán giá trị khác null chưa
                    if (isset($fromStageId) && isset($emailTemplateId) && isset($actionId)) {
                        // Sử dụng firstOrCreate trên Model StageEmailConfig để đảm bảo không trùng lặp
                        // Dựa trên sự kết hợp của cả 3 UUID.
                        // Sử dụng firstOrCreate và lấy về đối tượng để có thể lấy ID
                        $stageEmailConfig = StageEmailConfig::firstOrCreate(
                            [
                                // Điều kiện để tìm kiếm bản ghi đã tồn tại
                                'stage_id' => $fromStageId, // UUID của giai đoạn
                                'template_id' => $emailTemplateId, // UUID của mẫu email
                                'action_id' => $actionId,  // UUID của hành động
                                'process_version_id' => $process_version_id,
                            ]
                            // Không cần mảng thứ hai vì không có cột nào khác cần set giá trị khi tạo
                        );

                        // *** Lấy ID của bản ghi vừa tìm/tạo trong stage_email_configs ***
                        $stageEmailConfigId = $stageEmailConfig->id;
                    } // Kết thúc xử lý lưu vào stage_email_configs

                    // --- Xử lý Stage Email Condition ---
                    // Chỉ lưu nếu có ID từ stage_email_configs (vừa lấy ở trên), ID của condition chính,
                    // và condition_status không rỗng
                    if (isset($stageEmailConfigId) && isset($nestedConditionId)) {
                        // Sử dụng firstOrCreate để đảm bảo không trùng lặp liên kết
                        // giữa một cấu hình stage-email-action cụ thể ($stageEmailConfigId)
                        // và một điều kiện cụ thể ($nestedConditionId).
                        EmailCondition::firstOrCreate(
                            [
                                // Điều kiện để tìm kiếm liên kết đã tồn tại
                                'stage_email_config_id' => $stageEmailConfigId, // << Sử dụng ID vừa lấy
                                'condition_id' => $nestedConditionId,
                                'condition_status' => 'true',
                            ],
                        );
                    } // Kết thúc xử lý lưu vào email_conditions

                    // --- Xử lý Back To Stage ---
                    if (isset($process['back_to_stage']['slug'])) {
                        $backToStageSlug = trim($process['back_to_stage']['slug']);
                        // Ưu tiên kiểm tra map
                        if (isset($processedStageIdMap[$backToStageSlug])) {
                            $backToStageId = $processedStageIdMap[$backToStageSlug];
                        } else {
                            // 7. Nếu không có trong map, tìm trong CSDL bằng Model GiaiDoan
                            $backStageUuid = Stage::where('process_version_id', $process_version_id)
                                ->where('slug', $backToStageSlug)
                                ->value('id'); // Chỉ lấy giá trị cột 'id' (UUID)

                            // 8. Gán ID nếu tìm thấy
                            if ($backStageUuid) {
                                $backToStageId = $backStageUuid;
                            }
                        }
                    } else if (is_array($process['back_to_stage']) && count($process['back_to_stage']) === 1) {
                        $backToStageId = current($process['back_to_stage']); // Lấy định danh đặc biệt (vd: 'start')
                    }

                    /// --- Xử lý Stage Transition ---
                    // Chỉ lưu nếu có ID (UUID) hợp lệ cho cả ba thành phần
                    // Sử dụng isset() để kiểm tra biến đã được gán giá trị khác null chưa
                    if (isset($fromStageId) && isset($toStageId) && isset($actionId)) {
                        // Gọi firstOrCreate và gán kết quả (đối tượng Model) vào biến $stageTransition
                        $stageTransition = StageTransition::firstOrCreate(
                            [
                                // Điều kiện tìm kiếm
                                'from_stage_id' => $fromStageId,
                                'to_stage_id' => $toStageId,
                                'action_id' => $actionId,
                                'back_to_stage_id' => $backToStageId,
                                'process_version_id' => $process_version_id,
                            ]
                        );

                        // *** Lấy ID (UUID) từ đối tượng vừa tìm thấy hoặc tạo mới ***
                        $stageTransitionId = $stageTransition->id;
                    }// Kết thúc xử lý lưu vào stage_transitions

                    // --- Xử lý Stage Transition Condition ---
                    // Chỉ lưu nếu có ID từ stage_transitions (vừa lấy ở trên), ID của condition chính,
                    // và condition_status không rỗng
                    if (isset($stageTransitionId) && isset($conditionId)) {
                        // Sử dụng firstOrCreate để đảm bảo không trùng lặp liên kết
                        // giữa một cấu hình stage-email-action cụ thể ($stageEmailConfigId)
                        // và một điều kiện cụ thể ($conditionId).
                        StageTransitionCondition::firstOrCreate(
                            [
                                // Điều kiện để tìm kiếm liên kết đã tồn tại
                                'stage_transition_id' => $stageTransitionId, // << Sử dụng ID vừa lấy
                                'condition_id' => $conditionId,
                                'condition_status' => $process['condition_status'],
                            ],
                        );
                    } // Kết thúc xử lý lưu vào stage_transition_conditions

                    // --- Xử lý trường hợp lưu giai đoạn back lại ---
                    if (isset($process['back_to_stage']) && isset($process['action_back_to']) && isset($process['email_template_back'])) {
                        // --- Xử lý Action Back To---
                        if (is_array($process['action_back_to']) && count($process['action_back_to']) === 1) {
                            $actionBackToId = current($process['action_back_to']);
                        }
                        // --- Xử lý Email Template Back ---
                        // Kiểm tra xem email_template có tồn tại, là mảng và có 'name' không
                        if (isset($process['email_template_back']) && isset($process['email_template_back']['slug'])) {
                            $emailTemplateBackSlug = trim($process['email_template_back']['slug']);
                            $nestedConditionBackId = null; // ID cho condition *bên trong* email template

                            // 1. Xử lý condition lồng bên trong email_template trước
                            //    Kiểm tra cấu trúc ['condition']['value']
                            if (isset($process['email_template_back']['condition']['value']) && isset($process['email_template_back']['condition']['value']['slug'])) {
                                $nestedConditionBackSlug = trim($process['email_template_back']['condition']['value']['slug']);
                                $nestedOrConditionsBack = $process['email_template_back']['condition']['value']['or_conditions'] ?? null;

                                // Biến đổi 'p' trong orConditions lồng nhau
                                if (is_array($nestedOrConditionsBack)) {
                                    $this->transformOrConditionValues($nestedOrConditionsBack);
                                }

                                // Tìm hoặc tạo Condition lồng nhau này
                                if (isset($processedConditionIdMap[$nestedConditionBackSlug])) {
                                    $nestedConditionBackId = $processedConditionIdMap[$nestedConditionBackSlug];
                                } else {
                                    $nestedCondBack = Condition::firstOrCreate(
                                        [
                                            'process_version_id' => $process_version_id,
                                            'slug' => $nestedConditionBackSlug
                                        ],
                                        [
                                            'name' => $process['email_template_back']['condition']['value']['name'],
                                            'or_conditions' => $nestedOrConditionsBack !== null ? $nestedOrConditionsBack : null
                                        ]
                                    );
                                    $nestedConditionBackId = $nestedCondBack->id;
                                    $processedConditionIdMap[$nestedConditionBackSlug] = $nestedConditionBackId; // Cập nhật map chung
                                }
                            } // Kết thúc xử lý condition lồng nhau

                            // 2. Tìm hoặc tạo EmailTemplateBack, liên kết với condition_id (nếu có)
                            //    Kiểm tra map email trước
                            if (isset($processedEmailTemplateIdMap[$emailTemplateBackSlug])) {
                                $emailTemplateBackId = $processedEmailTemplateIdMap[$emailTemplateBackSlug];
                            } else {
                                // Kiểm tra và lưu file tương ứng
                                $file_name_news = [];
                                if (isset($email_template_back_files[$index])) {
                                    foreach ($email_template_back_files[$index] as $file) {
                                        if (is_string($file)) {
                                            $file_name_news[] = $file;
                                        } else {
                                            $file_name = $file->getClientOriginalName();
                                            $extension = $file->getClientOriginalExtension();
                                            // Tạo độ trễ 1 giây giữa mỗi lần upload
                                            sleep($sleep_second); 
                                            $current_time = now()->format('H.i.s');
                                            $current_date = now()->format('d.m.Y');
                                            $file_name_new = $file_name . '-' . $current_time . '-' . $current_date . '.' . $extension;
                                            $file_name_news[] = $file_name_new;
                                            //Lưu file chứng từ vào thư mục file_upload_emails
                                            $file->storeAs('file_upload_emails', $file_name_new);
                                        }   
                                    }
                                }

                                $emailTemplateBack = EmailTemplate::firstOrCreate(
                                    [
                                        'process_version_id' => $process_version_id,
                                        'slug' => $emailTemplateBackSlug
                                    ],
                                    [
                                        'name' => $this->nullIfEmpty($process['email_template_back']['name'] ?? null),
                                        'name_title' => $this->nullIfEmpty($process['email_template_back']['name_title'] ?? null),
                                        'from_email' => $this->extractNotKeyValue($process['email_template_back']['from_email']) ?? null,
                                        'to_emails' => $this->extractValues($process['email_template_back']['to_emails']) ?? null,
                                        'cc_emails' => $this->extractValues($process['email_template_back']['cc_emails']) ?? null,
                                        'bcc_emails' => $this->extractValues($process['email_template_back']['bcc_emails']) ?? null,
                                        'content' => $this->nullIfEmpty($process['email_template_back']['content'] ?? null),
                                        'files' => !empty($file_name_news) ? $file_name_news : null,
                                    ]
                                );
                                $emailTemplateBackId = $emailTemplateBack->id;
                                $processedEmailTemplateIdMap[$emailTemplateBackSlug] = $emailTemplateBackId; // Cập nhật map email
                            }

                            // Lưu vào bảng stage_email_configs cho trường hợp "Back"
                            if (isset($fromStageId) && isset($emailTemplateBackId) && isset($actionBackToId)) {
                                $stageEmailConfig = StageEmailConfig::firstOrCreate(
                                    [
                                        // Điều kiện để tìm kiếm bản ghi đã tồn tại
                                        'stage_id' => $fromStageId, // UUID của giai đoạn
                                        'template_id' => $emailTemplateBackId, // UUID của mẫu email
                                        'action_id' => $actionBackToId,  // UUID của hành động
                                        'process_version_id' => $process_version_id,
                                    ]
                                    // Không cần mảng thứ hai vì không có cột nào khác cần set giá trị khi tạo
                                );

                                // *** Lấy ID của bản ghi vừa tìm/tạo trong stage_email_configs ***
                                $stageEmailConfigId = $stageEmailConfig->id;
                            }

                            // --- Xử lý Stage Email Condition ---
                            // Chỉ lưu nếu có ID từ stage_email_configs (vừa lấy ở trên), ID của condition chính,
                            // và condition_status không rỗng
                            if (isset($stageEmailConfigId) && isset($nestedConditionBackId)) {
                                // Sử dụng firstOrCreate để đảm bảo không trùng lặp liên kết
                                // giữa một cấu hình stage-email-action cụ thể ($stageEmailConfigId)
                                // và một điều kiện cụ thể ($nestedConditionBackId).
                                EmailCondition::firstOrCreate(
                                    [
                                        // Điều kiện để tìm kiếm liên kết đã tồn tại
                                        'stage_email_config_id' => $stageEmailConfigId, // << Sử dụng ID vừa lấy
                                        'condition_id' => $nestedConditionBackId,
                                        'condition_status' => 'true',
                                    ],
                                );
                            } // Kết thúc xử lý lưu vào email_conditions
                        } // Kết thúc xử lý email_template_back
                    }

                    /// --- Xử lý Process Transition ---
                    // Chỉ lưu nếu có ID (UUID) hợp lệ cho cả ba thành phần
                    // Sử dụng isset() để kiểm tra biến đã được gán giá trị khác null chưa
                    if (isset($fromStageId) && isset($process['process_transition']['slug']) && isset($actionId)) {
                        // Gọi firstOrCreate và gán kết quả (đối tượng Model) vào biến $stageTransition
                        $processTransition = ProcessTransition::firstOrCreate(
                            [
                                // Điều kiện tìm kiếm
                                'slug' => $process['process_transition']['slug'],
                                'from_stage_id' => $fromStageId,
                                'to_process_id' => $this->extractNotKeyValue($process['process_transition']['to_process_id']) ?? null,
                                'action_id' => $actionId,
                                'process_version_id' => $process_version_id,
                            ],
                            [
                                'name' => $this->nullIfEmpty($process['process_transition']['name'] ?? null),
                                'type_create' => $this->nullIfEmpty($process['process_transition']['type_create'] ?? null),
                            ]
                        );

                        // *** Lấy ID (UUID) từ đối tượng vừa tìm thấy hoặc tạo mới ***
                        $processTransitionId = $processTransition->id;
                    } // Kết thúc xử lý lưu vào process_transitions

                    // --- Xử lý Stage Process Transition Condition ---
                    // Chỉ lưu nếu có ID từ stage_transitions (vừa lấy ở trên), ID của condition chính,
                    // và condition_status không rỗng
                    if (isset($processTransitionId) && isset($conditionId)) {
                        // Sử dụng firstOrCreate để đảm bảo không trùng lặp liên kết
                        // giữa một cấu hình stage-email-action cụ thể ($stageEmailConfigId)
                        // và một điều kiện cụ thể ($conditionId).
                        ProcessTransitionsCondition::firstOrCreate(
                            [
                                // Điều kiện để tìm kiếm liên kết đã tồn tại
                                'process_transition_id' => $processTransitionId, // << Sử dụng ID vừa lấy
                                'condition_id' => $conditionId,
                                'condition_status' => $process['condition_status'],
                            ],
                        );
                    } // Kết thúc xử lý lưu vào process_transitions_conditions

                    // dump($emailTemplateBackId, $actionBackToId);
                    // dd($setup_process);
                    // dump($fromStageId, $toStageId, $actionId, $conditionId, $emailTemplateId, $syncGroupId, $backToStageId);
                }
                // Lưu thông tin processVersion
                $this->saveField($fields, $processedStageIdMap, $process_version_id, $form_id);
            }

            // Cam kết giao dịch
            DB::commit();

            return response()->json([
                'status' => 'success',
                'process_id' => $process_id,
            ], 201);
        } catch (\Exception $e) {
            // Rollback giao dịch nếu có lỗi
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'error_code' => 'SERVER_ERROR',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function transformOrConditionValues(?array &$data): void // Trả về void, thay đổi $data trực tiếp
    {
        // 1. Kiểm tra đầu vào: Nếu không phải là mảng (ví dụ: null), dừng lại.
        if (!is_array($data)) {
            return;
        }
        // 2. Duyệt qua từng phần tử của mảng bằng tham chiếu (&value)
        foreach ($data as $key => &$value) {
            // 3. Nếu key là 'p' VÀ giá trị của nó ($value) là một mảng
            if ($key === 'p' && is_array($value)) {
                // Kiểm tra nếu toán tử 'o' là 'between'
                if (isset($data['o']) && $data['o'] === 'between') {
                    // Giữ nguyên giá trị của 'p' nếu toán tử là 'between'
                    continue;
                }
                // 3a. Lọc các phần tử con trong mảng 'p' ($value)
                // Chỉ giữ lại những phần tử là mảng VÀ có key 'value'
                $valid_elements = array_filter($value, function($item) {
                    return is_array($item) && isset($item['value']);
                });

                $invalid_elements = array_filter($value, function($item) {
                    return is_string($item);
                });

                // 3b. Dùng array_column để trích xuất chỉ các giá trị của key 'value'
                // 3c. Gán lại kết quả (mảng chỉ chứa các value) cho $value.
                // Vì $value là tham chiếu, điều này thay đổi trực tiếp mảng 'p' gốc.
                
                if (!empty($valid_elements)) {
                    $value = array_column($valid_elements, 'value');
                }

                if (!empty($invalid_elements)) {
                    $value = $invalid_elements;
                }
            }
            // 4. Nếu key không phải 'p' NHƯNG giá trị ($value) là một mảng
            else if (is_array($value)) {
                // 4a. Gọi đệ quy chính hàm này để xử lý tiếp mảng con $value
                // Giả định hàm này là một phương thức của class nên dùng $this->
                $this->transformOrConditionValues($value);
                // (Nếu là hàm độc lập bên ngoài class thì dùng: transformOrConditionValues($value);)
            }
            // 5. Nếu key không phải 'p' và giá trị cũng không phải mảng, bỏ qua phần tử này.
        }
        // 6. Hủy tham chiếu đến phần tử cuối cùng sau vòng lặp (good practice)
        unset($value);
    }
}
