<template>
    <CCol :xs="12">
        <!-- Biểu mẫu xuất file -->
        <CCol :xs="12" class="mb-3">
            <label class="mb-1">
                {{ $t('workflow.print_template.upload_template') }}
            </label>
            <FilePond
                :files="dataPrintTemplate.files"
                @updatefiles="(fileItemUploads) => updateFiles(fileItemUploads)"
                @addfile="onAddFiles"
                className="file-pond"
                :labelIdle="$t('validate_field.file_upload.label_idle')"
                :allowMultiple="true"
                :maxFiles="state.maxFiles"
                :maxFileSize="state.maxFileSize"
                :acceptedFileTypes="state.acceptedFileTypes"
                :labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
                :labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
                :fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_type_excel_words')}`"
                :labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
                :instantUpload="false"
                name="files"
                ref="files"
                credits="false"
                allow-reorder="true"
                item-insert-location="after"
                image-preview-min-height="60"
                image-preview-max-height="60"
            />
        </CCol>
        <!-- Từ khóa của trường tùy chỉnh -->
        <CCol :xs="12" class="mb-3">
            <label>{{ $t('workflow.print_template.use_keywords') }} {tu_khoa}</label>
            <CTable align="middle" responsive>
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th class="align-middle">
                                {{ $t('workflow.print_template.keyword') }}
                            </th>
                            <th class="align-middle">
                                {{ $t('workflow.print_template.display_name') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(field, indexField) in getCombinedFields" :key="indexField">
                            <td class="align-middle">
                                <span>&#123;{{ field.keyword }}&#125;</span>
                            </td>
                            <td class="align-middle">
                                <span>{{ field.display_name }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </CTable>
        </CCol>
        <CCardFooter>
            <div class="d-flex justify-content-end">
                <CButton 
                    type="button"
                    class="btn btn-light border m-1"
                    @click="closeFormPrintTemplate"
                >
                    <span class="text-uppercase">
                        {{ $t('workflow.print_template.close') }}
                    </span>
                </CButton>
                <CButton 
                    type="button"
                    class="btn btn-primary m-1"
                    @click="handleSubmitFormPrintTemplate"
                >
                    <span class="text-uppercase">
                        {{ $t('workflow.print_template.save_update') }}
                    </span>
                </CButton>
            </div>
        </CCardFooter>
    </CCol>
</template>

<script lang="ts">
import { defineComponent, reactive, computed } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import vueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize);

export default defineComponent({
    name: 'PrintTemplateAdd',
    components: {
        FilePond
    },
    emits: ['close-modal-print-template', 'add-print-template'],

    props: {
        dataPrintTemplate: {
            type: Object,
            default: {},
            required: true,
        },
        fieldSetups: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
        fieldCreateds: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        }, 
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const state = reactive({
            maxFiles: 50,
			maxFileSize: '5MB',
			acceptedFileTypes: [
				'application/msword', 
				'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
				'application/vnd.ms-excel', 
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			],
            keywords: [
                { keyword: 'title', display_name: 'Tên quy trình' },
                { keyword: 'code', display_name: 'Mã công việc' },
                { keyword: 'start', display_name: 'Bắt đầu quy trình' },
                { keyword: 'end', display_name: 'Kết thúc quy trình' },
                { keyword: 'start_plan', display_name: 'Bắt đầu dự kiến' },
                { keyword: 'end_plan', display_name: 'Kết thúc dự kiến' },
                { keyword: 'date_created', display_name: 'Ngày tạo quy trình' },
            ]
        });

        const closeFormPrintTemplate = () => {
            emit('close-modal-print-template');
		}

        const handleSubmitFormPrintTemplate = async () => {
            if (!props.dataPrintTemplate.files || props.dataPrintTemplate.files.length === 0) {
                $toast.open({
                    message: t('toast.message.upload_template_required'),
                    type: "warning",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                return;
            }
            closeFormPrintTemplate();
            $toast.open({
                message: t('toast.status.ACTION_SUCCESS'),
                type: "success",
                duration: 5000,
                dismissible: true,
                position: "bottom-right",
            });
        }

        // FilePond functions
        const updateFiles = (fileItemUploads: any) => {
			props.dataPrintTemplate.files = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

        const onAddFiles = (error: any, file: any) => {
			if (error) {
				props.dataPrintTemplate.files = props.dataPrintTemplate.files.filter((item: any) => item.name !== file.filename);
				$toast.open({
					message: `${error.main} - ${error.sub}`,
					type: "warning",
					duration: 10000,
					dismissible: true,
					position: "bottom-right",
				});
			}
		};

        const getCombinedFields = computed(() => {
            return [...props.fieldSetups, ...props.fieldCreateds, ...state.keywords];
        });

        return {
            state,
            closeFormPrintTemplate,
            handleSubmitFormPrintTemplate,
            updateFiles,
            onAddFiles,
            getCombinedFields,
        }
    },
});
</script>
<style type="text/css" scoped>
</style>