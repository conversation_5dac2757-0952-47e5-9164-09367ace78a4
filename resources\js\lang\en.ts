const en = {
    dashboard: 'Dashboard',
    page_404: 'Page not found',
    page_403: 'Access denied',
    
    auth: {
        login: 'Login',
        account_name: 'Account name*',
        password: 'Password*',
        reset_pass: 'Forgot password?',
        email_reset: 'Your email*',
        retrieve_pass: 'Retrieve password',
        confirm_reset: 'Confirm',
        back_login: 'Back to login page',
        title_logo: 'Internal approval platform',
        new_pass: 'Create new password',
        new_password: 'Enter new password*',
        new_password_confirm: 'Confirm new password*',
        reset_password: 'Reset password',
        lang: {
            vi: 'Vietnamese',
            en: 'English',
        }
    },
    dropdown_acc: {
        settings: 'System settings',
        account: 'Account',
        book: 'User guide',
        change_color: 'Change interface color',
        change_lang: 'Language',
        change_pass: 'Change password',
        logout: 'Logout',
    },
    sidebar: {
        list: 'List',
        home: 'Home',
        dashboard: 'Dashboard',
        system: {
            title: 'System',
            user: 'User',
            group: 'Group',
            department: 'Department',
            position: 'Position',
            cost_center: 'Cost-center',
            job_positions: 'Job positions',
        }, 
        setting: {
            title: 'Setting',
            email: 'Email',
            parameter: 'Parameter',
            tenant: 'Tenant',
            permission: 'Permission',
        },
        workplace: {
            title: 'Workplace',
            job: 'Job', 
            workflow: 'Workflow', 
        }
    },
    type_field: {
        varchar: 'Character',
        varchar_desc: 'Use for input data in the form of a single line of text',
        text: 'Text',
        text_desc: 'Use for input data in the form of multiple lines of text',
        integer: 'Integer',
        integer_desc: 'Use for input data in the form of an integer',
        float: 'Float',
        float_desc: 'Use for input data in the form of a float',
        date: 'Date',
        date_desc: 'Display a date selection table',
        time: 'Time',
        time_desc: 'Display a time selection table',
        select: 'List',
        select_desc: 'Use to create a list of choices',
        radio: 'Radio',
        radio_desc: 'Allow users to select one value from the list of choices',
        checklist: 'Checklist',
        checklist_desc: 'Allow users to select one or more values from the list of choices',
        user: 'User',
        user_desc: 'Drop-down list of users',
        department: 'Department',
        department_desc: 'Drop-down list of departments',
        file_upload: 'File upload',
        file_upload_desc: 'Allow users to upload one or more documents from their computer',
        formula: 'Formula',
        formula_desc: 'Create an excel formula using form keywords',
        table: 'Table',
        table_desc: 'A list of rows that users can define',
        object_system: 'System object',
        object_system_desc: 'Select data from tables in the system',
    },
    form: {
        name: 'Form name',
        description: 'Form description',
        save_update: 'Update',
        close: 'Close',
        field: {
            setting: 'Setting',
            created: 'Created field',
            setup: 'Setup field',
            display_name: 'Display name',
            keyword: 'Keyword',
            required: 'Required',
            column_width: 'Column width',
            order: 'Order',
            default_value: 'Default value',
            multiple_value: 'Allow multiple values',
            options: 'Options',
            multiple_file: 'Allow multiple files',
            placeholder: 'Placeholder',
            not_edit: 'Not allow to edit',
            min_character: 'Minimum number of characters',
            max_character: 'Maximum number of characters',
            min_number: 'Minimum number',
            max_number: 'Maximum number',
            formula: 'Formula',
            formula_sample: '= dongia * soluong',
            table: 'Table object',
            table_column: 'Table column',
            table_column_sub: 'Table column information',
            stages: 'Stages',
            children: 'Dependent field',
            name: 'Field name',
            is_active: 'Status',
            type: 'Field type',
            create_by: 'Created by',
            created_at: 'Created at',
            updated_at: 'Updated at',
            stage_id: 'Stage',
            action: 'Action',
            active: 'Active',
            unactive: 'Inactive',
            no: 'No',
            yes: 'Yes',
        },
        select: {
            choose_one: 'Choose one',
            select_multiple: 'Select multiple',
        },
        language: {
            vi: 'Vietnamese',
            en: 'English',
        },
        validate: {
            required: 'is required',
            name_length: 'must be between 1 and 200 characters',
            description_length: 'must be between 1 and 500 characters',
        }
    },
    validation: {
        required: '{field} is required',
        after: '{field} must be after {after}',
    },
    message: {
        error_occurred: 'An error occurred',
    },
    menu_options: {
        field: {
            edit: 'Edit',
            delete: 'Delete',
            unactive: 'Inactive',
        }
    }, 
    validate_field: {
        display_name: {
            required: 'is required',
            length: 'must be between 2 and 100 characters',
            to: 'to',
            characters: 'characters',
            must_between_text: 'must be between',
            must_between_number: 'must be between',
        },
        order: {
            integer: 'must be a positive integer',
        },
        placeholder: {
            length: 'must be between 2 and 100 characters'
        },
        keyword: {
            required: 'is required',
            length: 'must be between 2 and 100 characters'
        },
        min_character: {
            integer: 'must be a positive integer',
        },
        max_character: {
            integer: 'must be a positive integer',
        },
        default_value_varchar: {
            length: 'must be between 1 and 200 characters'
        },
        default_value_text: {
            length: 'must be between 1 and 50000 characters'
        },
        default_value_integer: {
            integer: 'must be a positive integer'
        },
        default_value_float: {
            integer: 'must be greater than or equal to 0'
        },
        min_number: {
            integer: 'must be a positive integer',
        },
        max_number: {
            integer: 'must be a positive integer',
        },
        table: {
            required: 'is required',
        },
        table_column: {
            required: 'is required',
        },
        formula: {
            required: 'is required',
        },
        stages: {
            required: 'is required',
        },
        file_upload: {
            label_idle: 'Drag & drop file here or click to select',
            label_idle_children: 'Select file',
            label_allowed: 'File format is not allowed',
            label_max_file_size_exceeded: 'File size exceeds maximum limit',
            label_max_file_size: 'Maximum file size is',
            label_expected_types: 'Support image, pdf, doc, docx, xls, xlsx, ...',
        }
    },

    job: {
        list: 'List of jobs',
        is_multiple: 'Create jobs for multiple people',
        add: 'Create new job',
        name: 'Job name',
        description: 'Job description',
        workflow: 'Apply workflow',
        job_manager: 'Job manager',
        followers: 'Followers',
        files: 'Attachments',
        save_update: 'Update',
        close: 'Close',
        detail: 'Job detail',
        general_info: 'General information',
        custom_field: 'Custom field',
        download: 'Download',
        file_not_previewable: 'Browser does not support this file type, download to view',
    },

    workflow: {
        option_system_default: {
            create_by: 'Created by',
            create_by_desc: 'Is created by',
            user: 'User',
            user_desc: 'Is user',
            approve_by: 'Approve by',
            approve_by_desc: 'Is approve by',
        },
        option_from_email: {
            email_desc: 'Is system email',
        },
        option_condition_system: {
            department: 'Department',
            job_position: 'Job position',
            rank: 'Rank',
        },
        option_processs: {
            department: 'Department',
            job_position: 'Job position',
            rank: 'Rank',
            name: 'Process name',
            description: 'Process description',
            create_by: 'Created by',
        },
        tab: {
            general_infor: '1. General information',
            form: '2. Form setting',
            process: '3. Process setting',
            process_group: 'Process group setting',
            email_conditions: 'Email condition setting',
            template_email: 'Email template setting',
            sync_groups: 'Sync group setting',
            conditions: 'Condition setting',
            actions: 'Action setting',
            stages: 'Stage setting',
        },
        validate: {
            required: 'is required',
        },
        object: {
            form: 'Form setting',
            process: 'Process setting',
        },
        operator: {
            equal: 'Equal',
            not_equal: 'Not equal',
            in: 'In (Belongs to)',
            not_in: 'Not in (Does not belong to)',
            greater: 'Greater than',
            lower: 'Lower than',
            not_greater: 'Lower than or equal to',
            not_lower: 'Greater than or equal to',
            contain: 'Contains',
            not_contain: 'Does not contain',
            empty: 'Empty',
            not_empty: 'Not empty',
            between: 'Between',
            other: 'Other',
        },
        create_form: 'Create form',
        create_form_desc: 'Form setting',
        add: 'Add new process',
        list: 'Process list',
        name: 'Process name',
        description: 'Process description',
        process_group: 'Process group',
        scope_use: 'Scope use',
        scope_use_desc: 'Only those employees in the scope of application can see and use this process',
        followers: 'Followers',
        process_manager: 'Process manager',
        process_manager_desc: 'Process manager has the right to edit/delete this process',
        job_manager: 'Job manager',
        job_manager_desc: 'Job manager has full authority over this process',
        save_update: 'Update',
        save_form: 'Save form',
        close: 'Close',
        save_draft: 'Save draft',
        choose: '-Choose-',
        feature: {
            stage_title: 'Stage',
            stage_desc: 'Stage setting',
            action_title: 'Action',
            action_desc: 'Action setting',
            condition_title: 'Condition',
            condition_desc: 'Condition setting',
            email_title: 'Send email',
            email_desc: 'Email setting',
            sync_group_title: 'Sync group',
            sync_group_desc: 'Sync group setting',
            process_transition_title: 'Process transition',
            process_transition_desc: 'Process transition setting',
            
        },
        stage: {
            from_stage: 'Start stage',
            to_stage: 'Transition stage',
            back_to_stage: 'Back stage',
            create: 'Create stage',
            edit: 'Edit stage',
            list: 'Stage list',
            name: 'Stage name',
            slug: 'Stage slug',
            description: 'Stage description',
            approver: 'Approver/ Executor',
            approver_desc: 'Approver/ Executor is a person or group of people who have the right to approve or not approve at this step',
            follower: 'Follower',
            follower_desc: 'Follower is a person or group of people who receive email notifications when the approval process runs to this step, and when the approval/event happens',
            comment: 'Comment',
            save_update: 'Update',
            close: 'Close',
            task: 'Task',
            validate: {
                required: 'is required',
                duplicate_slug: 'Slug already exists. Please select a different slug.'
            },
            option: {
                start: 'Start',
                done: 'Complete process',
                false: 'Cancel process',
            }
        },
        action: {
            title: 'Transition action',
            title_back: 'Back action',
            create: 'Create action',
            edit: 'Edit action',
            list: 'Action list',
            name: 'Action name',
            slug: 'Action slug',
            description: 'Action description',
            comment: 'Comment',
            comment_desc: 'Enter comment',
            save_update: 'Update',
            close: 'Close',
            execute: 'Execute action',
            validate: {
                required: 'is required',
                name_length: 'must be between 1 and 100 characters',
                description_length: 'must be between 1 and 200 characters',
                duplicate_slug: 'Slug already exists. Please select a different slug.'
            },
            option: {
                create: 'Create document',
                back_to: 'Back to'
            }
        },
        condition: {
            add: 'Condition',
            operator_and: 'And',
            operator_or: 'Or',
            and: 'Must be true for all conditions below',
            object: "Object",
            title: 'Transition condition',
            create: 'Create condition',
            edit: 'Edit condition',
            list: 'Condition list',
            name: 'Condition name',
            slug: 'Condition slug',
            or_conditions: 'Group condition',
            save_update: 'Update',
            close: 'Close',
            validate: {
                required: 'is required',
                duplicate_slug: 'Slug already exists. Please select a different slug.'
            },
            status: {
                success: 'Success',
                failure: 'Failure',
            }
        },
        email: {
            title: 'Send email',
            title_back: 'Back email',
            condition: 'Email condition',
            create: 'Create email',
            edit: 'Edit email',
            list: 'Email list',
            name: 'Title',
            slug: 'Slug title',
            content: 'Content',
            name_title: 'Email title',
            from_email: 'From email',
            to_emails: 'To email',
            cc_emails: 'Cc',
            bcc_emails: 'Bcc',
            files: 'File attachment',
            save_update: 'Update',
            close: 'Close',
            validate: {
                required: 'is required',
                duplicate_slug: 'Slug already exists. Please select a different slug.'
            },
        },
        print_template: {
            upload_template: 'Tải biểu mẫu lên',
            description: 'Mô tả biểu mẫu',
            save_update: 'Cập nhật',
            close: 'Đóng',
            use_keywords: 'Để sử dụng các từ khóa trong biểu mẫu, vui lòng đặt các từ khóa có dạng',
            keyword: 'Từ khóa',
            display_name: 'Tên hiển thị',
        },
        sync_group: {
            title: 'Sync group',
            create: 'Create sync group',
            edit: 'Edit sync group',
            list: 'Sync group list',
            name: 'Sync group name',
            slug: 'Sync group slug',
            save_update: 'Update',
            close: 'Close',
            validate: {
                required: 'is required',
                name_length: 'must be between 1 and 200 characters',
                duplicate_slug: 'Slug already exists. Please select a different slug.'
            },
        },
        process_transition: {
            title: 'Process transition',
            create: 'Create process transition',
            edit: 'Edit process transition',
            list: 'Process transition list',
            name: 'Process transition name',
            slug: 'Process transition slug',
            apply_process: 'Apply process',
            type_create_manual: 'Create manually',
            type_create_auto: 'Create automatically',
            save_update: 'Update',
            close: 'Close',
            validate: {
                required: 'is required',
                duplicate_slug: 'Slug already exists. Please select a different slug.'
            },
        },
        not_executable_tooltip: 'You do not have the right to view details or perform this action',
    },

    process_group: {
        create: 'Create process group',
        name: 'Process group name',
        save_update: 'Update',
        close: 'Close',
        validate: {
            required: 'is required',
            name_length: 'must be between 1 and 200 characters',
        },
    },

    account: {
        change_password: 'Change password',
        infomation: 'Account information',
        account_name: 'Account name',
        name: 'Display name',
        email: 'Email',
        language: 'Language',
        save_language: 'Save change'
    },

    change_password: {
        current_password: 'Current password',
        new_password: 'New password',
        new_password_confirm: 'Confirm new password',
        placeholder_current_password: 'Enter current password',
        placeholder_new_password: 'Enter new password',
        placeholder_new_password_confirm: 'Enter confirm new password',
        save: 'Save change',
    },

    paginate: {
        per_page: 'Display',
        record: 'record',
        page: 'Page',
    },

    swal: {
        warning: 'Warning',
        title: 'Confirm',
        text: 'Are you sure you want to perform this action?',
        confirm_button_text: 'Yes',
        cancel_button_text: 'No',
        already_exists: 'already exists',
    },

    option_tab_workflow: {
        all: 'All',
        active: 'Active',
        unactive: 'Inactive',
        save_draft: 'Save draft',
    },

    option_tab_job: {
        all: 'All',
        completed: 'Completed',
        pending: 'Pending',
        processing: 'Processing',
        cancel: 'Cancel',
    },
    option_tab_job_detail: {
        detail: 'Detail',
        workflow: 'Workflow',
    },

    menu_tab: {
        custom: 'Customize',
    },

    search: {
        title: 'Search',
        no_matching_records_found: 'No matching records found'
    },

    common: {
        no_data: '--',
        close: 'Close',
        loading: 'Loading...',
        save: 'Save',
        add: 'Add',
        remove: 'Remove',
        actions: 'Actions',
        edit: 'Edit',
        delete: 'Delete',
        cancel: 'Cancel',
        active: 'Active',
        inactive: 'Inactive',
        confirm_delete: 'Confirm Delete',
    },

    user: {
        list: 'User list',
        add: 'Add new user',
        account_name: 'Account name',
        full_name: 'Full name',
        role: 'Role',
        roles: 'Roles',
        email: 'Email',
        department: 'Department',
        rank: 'Rank',
        job_position: 'Job position',
        create_by: 'Created by',
        created_at: 'Created at',
        information: 'User information',
        role_management: 'Role management',
        update_role: 'Update role',
        no_roles_assigned: 'No roles assigned',
        roles_updated_at: 'Roles last updated',
        failed_to_load: 'Failed to load user information',
        current_roles: 'Current roles',
        available_roles: 'Available roles',
        all_roles_assigned: 'All roles assigned',
        status: {
            active: 'Active',
            inactive: 'Inactive'
        }
    },

    tenant: {
        list: 'Tenant List',
        add: 'Add Tenant',
        edit: 'Edit Tenant',
        name: 'Tenant Name',
        name_placeholder: 'Enter tenant name',
        domain_name: 'Domain Name',
        domain_name_placeholder: 'Enter domain name (e.g: example.com)',
        start_date: 'Start Date',
        end_date: 'End Date',
        unlimited: 'Unlimited',
        status: 'Status',
        is_active: 'Active',
        create_by: 'Created by',
        created_at: 'Created at',
        save_update: 'Update',
        close: 'Close',
        account_name: 'Account name',
        account_name_placeholder: 'Enter account name (e.g: Admin)',
        full_name: 'Full name',
        full_name_placeholder: 'Enter full name (e.g: John Doe)',
        email: 'Email',
        email_placeholder: 'Enter email (e.g: <EMAIL>)',
        password: 'Password',
        password_placeholder: 'Enter password',
        tab: {
            info: 'Information',
            account: 'Account',
        }
    },
    option_tab_tenant: {
        all: 'All',
        active: 'Active',
        unactive: 'Inactive',
    },
    toast: {
        message: {
            not_setting_condition: 'Object has not set condition',
            field_not_setup: 'Field has not set',
        },
        status: {
            ACTION_SUCCESS: 'Action successfully',
        },
        error_code: {
            KEY_DUPLICATE: 'The display key is not allowed to duplicate',
            SERVER_ERROR: 'An error occurred during execution',
            INVALID_FORM_DATA: 'Invalid form data',
            INVALID_WORKFLOW_DATA: 'Invalid workflow data',
            INVALID_FIELD_DATA: 'Invalid field data',
            INVALID_SETUP_PROCESS_DATA: 'Invalid setup process data',
            VALIDATION_LOGIN_FAILED: 'Please enter your account name and password',
        }
    }
}

export default en;