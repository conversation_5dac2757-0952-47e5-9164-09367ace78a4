import { useI18n } from "vue-i18n"
import { numberCommas } from "@/utils/utils"
import { evaluate } from 'mathjs'

export default function useDynamicFormLogic() {
	const { t } = useI18n()

	const transformObject = (obj: object) => {
		return Object.fromEntries(
			Object.entries(obj).map(([key, value]) => {
				if (key === 'order') return [key, value];
				if (value === 1) return [key, true];
				if (value === 0) return [key, false];
				if (typeof value === 'string' && isJsonArrayOrObject(value)) {
					return [key, JSON.parse(value)];
				}
				if (typeof value === 'string') {
					return [key, value];
				}
				return [key, value];
			})
		);
	};

	const isJsonArrayOrObject = (str: any) => {
		try {
			const parsed = JSON.parse(str);
			return typeof parsed === 'object' && parsed !== null;
		} catch (e) {
			return false;
		}
	};

	const mappedTypeDataField = (typeField: string) => {
		switch (typeField) {
			case 'VARCHAR':
				return 'text';
			case 'TEXT':
				return 'textarea';
			case 'INTEGER':
				return 'number';
			case 'FLOAT':
				return 'number';
			case 'DATE':
				return 'date';
			case 'TIME':
				return 'time';
			case 'SELECT':
				return 'MULTISELECT';
			case 'RADIO':
				return 'radio';
			case 'CHECKLIST':
				return 'checkbox';
			default:
				return typeField;
		}
	}

	const converFormFields = (dataFormFields: any, fieldTable: boolean) => {
		// VARCHAR, TEXT, INTEGER, FLOAT, DATE, TIME, SELECT, RADIO, CHECKLIST, DEPARTMENT, USER, FILEUPLOAD, FORMULA, TABLE, OBJECTSYSTEM
		if (dataFormFields.length > 0) {
			const sortedDataFields = dataFormFields.filter((item: any) => fieldTable ? item.parent_id !== null : item.parent_id === null).slice().sort((a: any, b: any) => a.order - b.order);
			const transformedData = sortedDataFields.map((dataField: any) => {
				// Lấy ngôn ngữ được chọn
				const currentLanguage: string = localStorage.getItem('app_language') || 'vi';
				// Lấy label dựa trên ngôn ngữ hiện tại
				const labelKey = currentLanguage === 'vi' ? 'display_name' : 'display_name_en';
				const placeholderKey = currentLanguage === 'vi' ? 'placeholder' : 'placeholder_en';
				const item = transformObject(dataField);
				
				// Initialize validation and validationMessages
				let validation = item.required ? 'required' : '';
				let validationMessages = {} as any;

				// Kiểm tra type và xử lý các thuộc tính khác nhau
				const formField = {
					type: mappedTypeDataField(item.type),
					label: item[labelKey],
					placeholder: item[placeholderKey],
					floatingLabel: 'true',
					class: 'form-control',
					name: item.keyword,
					validation: validation,
					validationMessages: validationMessages,
					column_width: item.column_width,
				} as any;
				
				// Chỉ thêm validationMessages.required nếu có required
				if (item.required) {
					if (item.type === 'RADIO' || item.type === 'CHECKLIST') {
						formField.legendClass = 'required-label';
					} else {
						formField.labelClass = 'required-label';
					}
					validationMessages.required = `${item[labelKey]} ${t('validate_field.display_name.required')}`;
				}

				// Nếu có default_value thì thêm vào formField
				if (item.default_value !== null) {
					formField.value = item.default_value;
				}

				// Nếu có not_edit thì thêm vào formField
				if (item.not_edit) {
					formField.disabled = item.not_edit;
				}

				// Nếu type là "RADIO", "CHECKLIST", "TIME", "DATE", thiết lập floatingLabel thành false
				if (item.type === 'RADIO' || item.type === 'CHECKLIST' || item.type === 'DATE' || item.type === 'TIME') {
					formField.floatingLabel = 'false';
				}

				// Chỉ thêm length khi có min_equal và max_equal cho type "text" và "textarea"
				if ((item.type === 'VARCHAR' || item.type === 'TEXT') && item.min_equal != null && item.max_equal != null) {
					const min = parseInt(item.min_equal, 10);
					const max = parseInt(item.max_equal, 10);
					
					if (!isNaN(min) && !isNaN(max)) {
						validation += (validation ? '|' : '') + `length:${min},${max}`;
						validationMessages.length = `${item[labelKey]} ${t('validate_field.display_name.must_between_text')} ${min} ${t('validate_field.display_name.to')} ${max} ${t('validate_field.display_name.characters')}`;
					}
				}

				// Thêm validation 'between' cho type 'number'
				if (item.type === 'INTEGER' && item.min_equal != null && item.max_equal != null) {
					const min = parseInt(item.min_equal, 10);
					const max = parseInt(item.max_equal, 10);

					if (!isNaN(min) && !isNaN(max)) {
						validation += (validation ? '|' : '') + `between:${min},${max}`;
						validationMessages.between = `${item[labelKey]} ${t('validate_field.display_name.must_between_number')} ${min} ${t('validate_field.display_name.to')} ${max}`;
					}
				}

				// Gán lại giá trị của validation vào formField nếu có
				if (validation) {
					formField.validation = validation;
				}

				// Bỏ các key không có giá trị
				Object.keys(formField).forEach(key => {
					if (!formField[key] && formField[key] !== 0) {
						delete formField[key]; // Loại bỏ key nếu không có giá trị
					}
				});
				
				// Thêm options cho type 'RADIO' và 'CHECKLIST'
				if ((item.type === 'RADIO' || item.type === 'CHECKLIST') && item.options) {
					formField.options = item.options;
				}

				// Thêm options và multiple cho type 'select'
				if (item.type === 'SELECT') {
					formField.options = item.options;
					// Nếu có multiple thì thêm vào formField
					formField.multiple = item.multiple;
					// Nếu có not_edit thì thêm vào formField
					formField.disabled = item.not_edit;
				}

				if (item.type === 'USER' || item.type === 'DEPARTMENT' || item.type === 'FILEUPLOAD' || item.type === 'OBJECTSYSTEM') {
					// Nếu có multiple thì thêm vào formField
					formField.multiple = item.multiple;
					// Nếu có not_edit thì thêm vào formField
					formField.disabled = item.not_edit;
				}

				// Nếu có parent_id thì thêm vào formField
				if (item.parent_id !== null) {
					formField.parent_id = item.parent_id;
				}

				// Nếu có children thì thêm vào formField
				if (item.children.length > 0) {
					formField.childrens = item.children.map((child: any) => {
						return transformObject(child);
					});
				}

				if (item.type === 'OBJECTSYSTEM') {
					// Nếu có column_table thì thêm vào formField
					if (item.column_table !== null) {
						formField.column_table = item.column_table;
					}
					// Nếu có column_table thì thêm vào formField
					if (item.object_table !== null) {
						formField.object_table = item.object_table;
					}
					// Nếu có sub_column_table thì thêm vào formField
					if (item.sub_column_table !== null) {
						formField.sub_column_table = item.sub_column_table;
					}
				}
				
				return formField;
			});

			return transformedData;
		} else {
			return [];
		}
	}

	const removeCommas = (value: any) => {
		return value.replace(/,/g,'');
	}

	// 🎯 SIÊU ĐỠN GIẢN: Một hàm duy nhất xử lý tất cả
	const getFieldValue = (field: any) => {
		// Ưu tiên: value có sẵn > default_value > giá trị mặc định theo type
		if (field.value !== undefined && field.value !== null) return field.value;
		if (field.default_value !== undefined && field.default_value !== null) return field.default_value;

		// Map đầy đủ cho tất cả field types trong hệ thống (CHỮ HOA)
		const defaults = {
			// Text types
			VARCHAR: null,
			TEXT: null,

			// Number types
			INTEGER: 0,
			FLOAT: 0,
			FORMULA: 0,

			// Date/Time types
			DATE: null,
			TIME: null,

			// Selection types (có thể multiple)
			SELECT: field.multiple ? [] : null,
			MULTISELECT: field.multiple ? [] : null,
			RADIO: null,
			CHECKLIST: field.multiple ? [] : null,

			// System object types (có thể multiple)
			USER: field.multiple ? [] : null,
			DEPARTMENT: field.multiple ? [] : null,
			OBJECTSYSTEM: field.multiple ? [] : null,

			// File types
			FILEUPLOAD: [],

			// Boolean types
			CHECKBOX: false,

			// Table type (không cần default value vì xử lý riêng)
			TABLE: null
		};

		return defaults[field.type] ?? null; // Fallback cho types chưa định nghĩa
	}

	const initializeItem = (childrens: any) => {
		const newItem = {} as any;
		childrens.forEach((child: any) => {
			// Sử dụng hàm siêu đơn giản
			newItem[child.keyword] = getFieldValue(child);

			// Xử lý formula nếu có
			if (typeof child.default_value === 'string' && child.default_value.startsWith('=')) {
				newItem['keyword_formula'] = child.keyword;
				newItem['original_formula'] = child.default_value;
			}
		});

		return newItem;
	};

	const initializeItemChildrens = (transformedFields: any[], itemChildrens: { [key: string]: any[] }) => {
		transformedFields.forEach((field: any) => {
			if (field.type === 'TABLE' && field.childrens) {
				itemChildrens[field.name] = [{ ...initializeItem(field.childrens) }];
			}
		});
	};

	const calculateFormulaWithFormData = (formula: string, results: any, formData: any) => {
		try {
			// Kiểm tra input
			if (!formula || typeof formula !== 'string') {
				return 0;
			}

			// Loại bỏ dấu '=' ở đầu công thức
			let expr = formula.replace(/^=+/, '').trim();

			// Nếu sau khi loại bỏ = thì không còn gì
			if (!expr) {
				return 0;
			}

			// Kết hợp dữ liệu từ formData và kết quả công thức đã tính
			const data = { ...formData, ...results };

			// Thay thế các biến trong công thức bằng giá trị thực tế
			expr = expr.replace(/\b[a-zA-Z_]\w*\b/g, (name) => {
				const value = data[name];
				// console.log(`Thay thế biến "${name}" bằng giá trị:`, value);

				// Kiểm tra kỹ hơn: null, undefined, empty string đều thành 0
				if (value === null || value === undefined || value === '') {
					return '0';
				}

				// Nếu là string nhưng có thể convert thành number
				if (typeof value === 'string') {
					const numValue = parseFloat(value.replace(/,/g, ''));
					return isNaN(numValue) ? '0' : numValue.toString();
				}

				// Nếu là number
				if (typeof value === 'number') {
					return value.toString();
				}

				// Nếu là boolean, convert thành số
				if (typeof value === 'boolean') {
					return value ? '1' : '0';
				}

				// Fallback cho các type khác
				return '0';
			});

			// console.log('Biểu thức sau khi thay thế:', expr);

			// Kiểm tra biểu thức có hợp lệ không (chỉ chứa số, toán tử, dấu ngoặc)
			if (!/^[0-9+\-*/().\s]+$/.test(expr)) {
				console.warn('Invalid formula expression:', expr);
				return 0;
			}

			// Tính toán biểu thức
			const result = evaluate(expr);
			// console.log('Kết quả tính toán:', result);

			// Đảm bảo kết quả là số
			return typeof result === 'number' && !isNaN(result) ? result : 0;
		} catch (error) {
			console.error('Error calculating formula:', error);
			console.error('Formula:', formula);
			console.error('FormData:', formData);
			console.error('Results:', results);
			return 0;
		}
	};

	const extractSumPlus = (str: string) => {
		// Định nghĩa RegEx để tìm phần trong ngoặc
		// Tìm nội dung trong ngoặc đơn, có tiền tố "@SUMPLUS"
		const regex = /\(([^)]+)\)/;
		// const regex = /@SUMPLUS\(([^)]+)\)/;
		const match = str.match(regex);

		if (match) {
			const insideParentheses = match[1];

			// Tách chuỗi bằng dấu chấm
			const [value1, value2] = insideParentheses.split('.');

			// Kiểm tra nếu có đủ hai giá trị
			if (value1 && value2) {
				return [value1, value2];
			} else {
				return null;
			}
		}

		return null;
	};

	const sumColumn = (field: any, itemChildrens: any) => {
		const SUMPLUS = field.value;
		const result = extractSumPlus(SUMPLUS);
		if (result) {
			const [tableName, columnName] = result;
			return itemChildrens[tableName].reduce((total: number, itemChildren: any) => {
				const value = parseFloat(removeCommas(itemChildren[columnName])) || 0;
				return total + value;
			}, 0);
		} else {
			console.log("Không thể bóc tách các giá trị.");
			return 0;
		}
	};

	const initializeValues = (formFields: any[], formData: any) => {
		formFields.forEach((field: any) => {
			// Sử dụng hàm siêu đơn giản
			formData[field.name] = getFieldValue(field);
		});
	};

	const addItem = (field: any, itemChildrens: any) => {
		if (!itemChildrens[field.name]) {
			itemChildrens[field.name] = [];
		}
		itemChildrens[field.name].push({ ...initializeItem(field.childrens) });
	};

	const removeItem = (field: any, itemIndex: number, itemChildrens: any) => {
		if (itemChildrens[field.name] && itemChildrens[field.name][itemIndex]) {
			itemChildrens[field.name].splice(itemIndex, 1);
		}
	};

	const showSubColumnTable = (optionSelected: any, keyName: string, subColumnTableDescription: any, subColumnTableOptionSelected: any) => {
		// Reset dữ liệu trước khi cập nhật
		subColumnTableDescription[keyName] = {};
		subColumnTableOptionSelected[keyName] = {};

		// Kiểm tra optionSelected khác null va đối tượng khác rỗng
		if (Array.isArray(optionSelected) == false) {
			if (optionSelected !== null && Object.keys(optionSelected).length !== 0) {
				subColumnTableDescription[keyName] = optionSelected.sub_column_table_description;
				const subColumnTableOptionSelectedItem = optionSelected.sub_column_table_data_options.find(
					(option: any) => option.id === optionSelected.value
				);
				if (subColumnTableOptionSelectedItem) {
					subColumnTableOptionSelected[keyName] = subColumnTableOptionSelectedItem;
				}
			}
		}
	};

	const showSubColumnTableChildren = (
		optionSelected: any,
		itemIndex: number,
		keyName: string,
		subColumnTableDescriptionChildren: any,
		subColumnTableOptionSelectedChildren: any
	) => {
		// Khởi tạo nếu chưa tồn tại
		if (!subColumnTableDescriptionChildren[keyName]) {
			subColumnTableDescriptionChildren[keyName] = {};
		}
		if (!subColumnTableOptionSelectedChildren[keyName]) {
			subColumnTableOptionSelectedChildren[keyName] = {};
		}

		// Reset dữ liệu trước khi cập nhật
		subColumnTableDescriptionChildren[keyName][itemIndex] = {};
		subColumnTableOptionSelectedChildren[keyName][itemIndex] = {};

		// Kiểm tra optionSelected có phải là array hay không
		if (Array.isArray(optionSelected) == false) {
			// Kiểm tra optionSelected khác null và đối tượng khác rỗng
			if (optionSelected !== null && Object.keys(optionSelected).length !== 0) {
				subColumnTableDescriptionChildren[keyName][itemIndex] = optionSelected.sub_column_table_description;
				const subColumnTableOptionSelectedChildrenItem = optionSelected.sub_column_table_data_options.find(
					(option: any) => option.id === optionSelected.value
				);
				if (subColumnTableOptionSelectedChildrenItem) {
					subColumnTableOptionSelectedChildren[keyName][itemIndex] = subColumnTableOptionSelectedChildrenItem;
				}
			}
		}
	};

	const updateFiles = (fileItemUploads: any, fieldName: string, formData: any) => {
		formData[fieldName] = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem: any) => fileItem.file) : null;
	};

	const updateFileChildrens = (fileItemUploads: any, itemChildren: any, fieldChildrenName: string) => {
		itemChildren[fieldChildrenName] = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem: any) => fileItem.file) : null;
	};

	const formattedFormulaChildrenResults = (nameKey: string, itemChildrens: any, formData?: any) => {
		const items = itemChildrens[nameKey];
		if (!items) return [];

		items.forEach((item: any) => {
			// Kiểm tra nếu có original_formula và nó không phải là false
			if (!item.original_formula == false) {
				const calculatedValue = calculateFormulaWithFormData(item.original_formula, item, formData);
				item[item.keyword_formula] = numberCommas(calculatedValue); // Format và cập nhật kết quả
			}
		});

		return items;
	};

	// Hàm tiện ích để đảm bảo tất cả các field đều có trong formData
	const ensureAllFieldsInFormData = (formFields: any[], formData: any, itemChildrens: any) => {
		formFields.forEach((field: any) => {
			if (field.type !== 'TABLE' && !(field.name in formData)) {
				// Khởi tạo giá trị mặc định cho field chưa có trong formData
				formData[field.name] = getFieldValue(field);
			}

			// Xử lý TABLE fields - đảm bảo tất cả field con đều có giá trị
			if (field.type === 'TABLE' && field.childrens) {
				if (!itemChildrens[field.name]) {
					itemChildrens[field.name] = [];
				}

				// Đảm bảo mỗi item trong table đều có tất cả các field con
				itemChildrens[field.name].forEach((item: any) => {
					field.childrens.forEach((child: any) => {
						if (!(child.keyword in item)) {
							// Khởi tạo giá trị mặc định cho field con chưa có
							item[child.keyword] = getFieldValue(child);
						}
					});
				});
			}
		});
	};

	return {
		transformObject,
		isJsonArrayOrObject,
		mappedTypeDataField,
		converFormFields,
		calculateFormulaWithFormData,
		removeCommas,
		extractSumPlus,
		sumColumn,
		getFieldValue, // 🎯 Hàm siêu đơn giản duy nhất
		initializeItem,
		initializeItemChildrens,
		initializeValues,
		addItem,
		removeItem,
		showSubColumnTable,
		showSubColumnTableChildren,
		updateFiles,
		updateFileChildrens,
		formattedFormulaChildrenResults,
		ensureAllFieldsInFormData
	}
}
