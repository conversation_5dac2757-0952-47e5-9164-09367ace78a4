<template>
	<template v-for="fieldValue in fieldValues" :key="fieldValue.id">
		<CCol :md="fieldValue.field?.type === 'TABLE' ? 12 : 6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ fieldValue?.label }}
			</CFormLabel>
			<div class="fw-normal">
				<template v-if="fieldValue.field?.type === 'FILEUPLOAD'">
					<div v-if="getFileList(fieldValue).length > 0">
						<div v-for="(file, index) in getFileList(fieldValue)" :key="index">
							<a 
								href="#" 
								@click.prevent="handleFileClick(file)"
								class="fw-normal text-decoration-none"
							>
								<p>{{ getFileName(file) }}</p>
							</a>
						</div>
					</div>
					<div v-else>
						{{ $t('common.no_data') }}
					</div>
				</template>
				<template v-else-if="fieldValue.field?.type === 'TABLE' && fieldValue.table_values && fieldValue.table_values.length > 0">
					<CTable align="middle" responsive>
						<table class="table table-hover custom-table">
							<thead>
								<tr>
									<th class="align-middle"
										v-for="(value, key) in fieldValue.table_values[0]"
										:key="key"
										:data-object-system="fieldValue.table_columns_type?.[key] === 'OBJECTSYSTEM' ? true : undefined"
									>
										<div class="d-flex">
											<span class="object-main-title">{{ key }}</span>
											<template v-if="fieldValue.table_columns_type?.[key] === 'OBJECTSYSTEM' && Array.isArray(value) && value.length > 0 && typeof value[0] === 'object'">
												<div class="d-flex">
													<template v-for="(item, key_item) in Object.keys(value[0]).filter(key => key !== 'main')" :key="key_item">
														<small class="text-muted object-field-item">{{ item }}</small>
													</template>
												</div>
											</template>
										</div>
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(row, rowIndex) in fieldValue.table_values" :key="rowIndex">
									<td class="align-middle"
										v-for="(value, key) in row"
										:key="key"
										:data-object-system="fieldValue.table_columns_type?.[key] === 'OBJECTSYSTEM' ? true : undefined"
									>
										<template v-if="isColumnTypeFileUpload(fieldValue, key)">
											<div v-if="getFileArrayFromValue(value).length > 0">
												<div v-for="(file, fileIndex) in getFileArrayFromValue(value)" :key="fileIndex">
													<a 
														href="#" 
														@click.prevent="handleFileClick(file)"
														class="fw-normal text-decoration-none"
													>
														{{ getFileName(file) }}
													</a>
												</div>
											</div>
											<div v-else>
												{{ $t('common.no_data') }}
											</div>
										</template>
										<template v-else>
											<div class="d-flex">
												<div class="object-main-title">{{ formatTableValue(value) }}</div>
												<template v-if="fieldValue.table_columns_type?.[key] === 'OBJECTSYSTEM' && Array.isArray(value)">
													<template v-for="(obj, objIndex) in value" :key="objIndex">
														<div class="d-flex" v-if="typeof obj === 'object' && obj !== null">
															<template v-for="(val, objKey) in obj" :key="objKey">
																<template v-if="String(objKey) !== 'main'">
																	<small class="text-muted object-field-item">{{ val }}</small>
																</template>
															</template>
														</div>
													</template>
												</template>
											</div>
										</template>
									</td>
								</tr>
							</tbody>
						</table>
					</CTable>
				</template>
				<template v-else>
					{{ getFieldValue(fieldValue) }}
				</template>
			</div>
		</CCol>
	</template>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import useJobFieldValues from '@/composables/useJobFieldValues'
import useFiles from '@/composables/files'

interface FieldValue {
	id: number;
	label: string;
	field?: {
		type: string;
	};
	extracted_values?: any[];
	field_value?: any;
	table_values?: any[];
	table_columns_type?: Record<string, string>;
}

export default defineComponent({
	name: 'JobFieldValues',

	props: {
		fieldValues: {
			type: Object,
			required: true,
			default: () => {}
		}
	},

	emits: ['file-click'],

	setup(props, { emit }) {
		const {
			getFileList,
			getFieldValue,
			formatTableValue,
			isColumnTypeFileUpload,
			getFileArrayFromValue
		} = useJobFieldValues();

		const { getFileName } = useFiles();

		const handleFileClick = (file: string) => {
			emit('file-click', file);
		};

		return {
			getFileList,
			getFieldValue,
			formatTableValue,
			isColumnTypeFileUpload,
			getFileArrayFromValue,
			getFileName,
			handleFileClick
		};
	}
});
</script>

<style scoped>
.object-field-item {
	display: inline-block;
	min-width: 200px;
	white-space: normal;
	word-wrap: break-word;
	padding: 0 10px;
}
.object-main-title {
	display: inline-block;
	min-width: 250px;
	white-space: normal;
	word-wrap: break-word;
	padding-right: 15px;
}

.custom-table {
	table-layout: auto;
	width: 100%;
}

.custom-table th, .custom-table td {
	white-space: normal;
	word-wrap: break-word;
	overflow-wrap: break-word;
	padding: 8px;
}

/* Loại bỏ giới hạn độ rộng cho cột OBJECTSYSTEM */
.custom-table th[data-object-system], 
.custom-table td[data-object-system] {
	width: auto;
}

/* Đảm bảo các cột tự động điều chỉnh theo nội dung */
.custom-table th {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>

