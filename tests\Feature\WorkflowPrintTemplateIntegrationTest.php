<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PrintTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class WorkflowPrintTemplateIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('local');
    }

    public function test_print_template_files_are_cleaned_up_when_database_transaction_fails()
    {
        // Arrange: Create fake files
        $file1 = UploadedFile::fake()->create('template1.docx', 100);
        $file2 = UploadedFile::fake()->create('template2.pdf', 200);
        
        // Simulate a scenario where files are uploaded but database operation fails
        $processVersionId = 'invalid-process-version-id';
        
        // Act: Try to save print templates with invalid process version ID
        // This should trigger database constraint violation
        
        $initialFileCount = count(Storage::allFiles());
        
        try {
            // Force a database error by using a transaction that will fail
            DB::transaction(function () use ($file1, $file2, $processVersionId) {
                // Upload files first (this should succeed)
                $fileUploadService = app('App\Services\FileUploadService');
                $uploadedFiles = $fileUploadService->uploadTemplateFiles([$file1, $file2], 'file_templates');
                
                // Verify files were uploaded
                $this->assertNotEmpty($uploadedFiles);
                $this->assertCount(2, $uploadedFiles['file_names']);
                
                // Now try to create database records with invalid data
                // This should fail and trigger cleanup
                foreach ($uploadedFiles['file_names'] as $key => $fileName) {
                    PrintTemplate::create([
                        'file_name' => $fileName,
                        'file_path' => $uploadedFiles['file_path'][$key],
                        'original_filename' => $uploadedFiles['original_filename'][$key],
                        'type' => $uploadedFiles['type'][$key],
                        'process_version_id' => null, // This will cause constraint violation
                    ]);
                }
            });
        } catch (\Exception $e) {
            // Expected to fail due to constraint violation
            $this->assertStringContainsString('NOT NULL constraint failed', $e->getMessage());
        }
        
        // Assert: Verify no database records were created
        $this->assertDatabaseCount('print_templates', 0);
        
        // Note: In a real scenario with the improved savePrintTemplate method,
        // files would be cleaned up. Here we're testing the database rollback.
    }

    public function test_successful_print_template_creation_with_real_files()
    {
        // Arrange
        $file1 = UploadedFile::fake()->create('template1.docx', 100);
        $file2 = UploadedFile::fake()->create('template2.pdf', 200);
        
        // Create a valid process version ID (you might need to create related records)
        $processVersionId = 'valid-process-version-id';
        
        // Act
        $fileUploadService = app('App\Services\FileUploadService');
        $uploadedFiles = $fileUploadService->uploadTemplateFiles([$file1, $file2], 'file_templates');
        
        // Verify files were uploaded to storage
        $this->assertNotEmpty($uploadedFiles);
        $this->assertCount(2, $uploadedFiles['file_names']);
        
        // Verify files exist in storage
        foreach ($uploadedFiles['file_path'] as $filePath) {
            $this->assertTrue(Storage::exists($filePath));
        }
        
        // Create database records
        $createdTemplates = [];
        foreach ($uploadedFiles['file_names'] as $key => $fileName) {
            $template = PrintTemplate::create([
                'file_name' => $fileName,
                'file_path' => $uploadedFiles['file_path'][$key],
                'original_filename' => $uploadedFiles['original_filename'][$key],
                'type' => $uploadedFiles['type'][$key],
                'process_version_id' => $processVersionId,
            ]);
            $createdTemplates[] = $template;
        }
        
        // Assert
        $this->assertCount(2, $createdTemplates);
        $this->assertDatabaseCount('print_templates', 2);
        
        // Verify database records
        $this->assertDatabaseHas('print_templates', [
            'original_filename' => 'template1.docx',
            'type' => 'docx',
            'process_version_id' => $processVersionId
        ]);
        
        $this->assertDatabaseHas('print_templates', [
            'original_filename' => 'template2.pdf',
            'type' => 'pdf',
            'process_version_id' => $processVersionId
        ]);
        
        // Verify files still exist in storage
        foreach ($uploadedFiles['file_path'] as $filePath) {
            $this->assertTrue(Storage::exists($filePath));
        }
    }

    public function test_file_upload_service_handles_multiple_files_correctly()
    {
        // Arrange
        $files = [
            UploadedFile::fake()->create('template1.docx', 100),
            UploadedFile::fake()->create('template2.pdf', 200),
            UploadedFile::fake()->create('template3.xlsx', 150),
        ];
        
        // Act
        $fileUploadService = app('App\Services\FileUploadService');
        $result = $fileUploadService->uploadTemplateFiles($files, 'file_templates');
        
        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('file_names', $result);
        $this->assertArrayHasKey('file_path', $result);
        $this->assertArrayHasKey('original_filename', $result);
        $this->assertArrayHasKey('type', $result);
        
        $this->assertCount(3, $result['file_names']);
        $this->assertCount(3, $result['file_path']);
        $this->assertCount(3, $result['original_filename']);
        $this->assertCount(3, $result['type']);
        
        // Verify original filenames are preserved
        $this->assertEquals(['template1.docx', 'template2.pdf', 'template3.xlsx'], $result['original_filename']);
        
        // Verify file types are extracted correctly
        $this->assertEquals(['docx', 'pdf', 'xlsx'], $result['type']);
        
        // Verify files exist in storage
        foreach ($result['file_path'] as $filePath) {
            $this->assertTrue(Storage::exists($filePath));
        }
        
        // Verify file names have timestamp format
        foreach ($result['file_names'] as $fileName) {
            $this->assertMatchesRegularExpression('/.*-\d{2}\.\d{2}\.\d{2}-\d{2}\.\d{2}\.\d{4}\..*/', $fileName);
        }
    }
}
